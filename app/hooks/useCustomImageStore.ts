import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { validateImageFile, type ImageValidationResult } from '../ImageMange/imageConfig'
import { BackgroundTypeEnum } from './useBackgroundStore'

// 图片详情接口
interface ImageDetails {
    id: string
    file: File
    previewUrl: string
}

// 自定义图片模态框状态接口
interface CustomImageState {
    image: ImageDetails | null // 当前上传的图片数据
    isDragging: boolean // 是否正在拖拽文件到放置区域
    error: string | null // 验证或处理错误信息
}

// 自定义图片模态框动作接口
interface CustomImageActions {
    // 处理文件上传、验证和状态更新
    uploadImage: (file: File) => void

    // 清除当前图片并重置状态
    clearImage: () => void

    // 手动设置拖拽状态以提供UI反馈
    setDragging: (isDragging: boolean) => void
}

// 组合状态和动作的类型
export type CustomImageStore = CustomImageState & CustomImageActions

/**
 * 自定义图片模态框的Zustand存储
 * 用于管理模态框内的图片上传状态，独立于其他存储
 */
export const useCustomImageStore = create<CustomImageStore>()(
    devtools(
        set => ({
            // 初始状态
            image: null,
            isDragging: false,
            error: null,

            /**
             * 上传图片并验证
             * @param file - 用户选择的图片文件
             * 处理流程：
             * 1. 验证文件类型和大小
             * 2. 验证失败则设置错误信息
             * 3. 验证成功则生成预览URL并更新状态
             *
             * 示例：
             * - 文件大小：最大5MB
             * - 文件类型：JPG、PNG
             * - 成功时：image状态更新为包含文件和预览URL的对象
             */
            uploadImage: (file: File): void => {
                // 验证文件
                const validationResult = validateImageFile(file, { showToast: false })

                if (!validationResult.isValid) {
                    // 验证失败，显示错误提示
                    const errorMessage = validationResult.errorMessage || '图片验证失败'
                    toast.error(errorMessage)
                    set({ error: errorMessage })
                    return
                }

                // 验证成功，清除之前的错误
                set({ error: null })

                // 生成唯一ID
                const id = `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

                // 创建预览URL
                const previewUrl = URL.createObjectURL(file)

                // 更新状态
                set({
                    image: {
                        id,
                        file,
                        previewUrl,
                    },
                })

                // 自动切换到图片背景类型
                // TODO: 需要通过统一状态管理来设置背景类型
                // useUnifiedStore.getState().setImageBackground()

                // 显示成功提示
                toast.success(`图片上传成功：${file.name}`)
            },

            /**
             * 清除当前图片并重置状态
             * 释放预览URL的内存并清空所有相关状态
             */
            clearImage: (): void => {
                set(state => {
                    // 如果存在预览URL，释放内存
                    if (state.image?.previewUrl) {
                        URL.revokeObjectURL(state.image.previewUrl)
                    }

                    // 显示清除提示
                    toast.success('图片已清除')

                    return {
                        image: null,
                        error: null,
                        isDragging: false,
                    }
                })
            },

            /**
             * 设置拖拽状态
             * @param isDragging - 是否正在拖拽
             * 用于提供拖拽时的视觉反馈
             */
            setDragging: (isDragging: boolean): void => {
                set({ isDragging })
            },
        }),
        {
            name: 'custom-image-store',
        },
    ),
)
