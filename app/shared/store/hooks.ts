/**
 * @fileoverview 便捷的状态管理 Hooks 导出
 * @description 提供简化的导入路径和常用的组合 hooks
 */

// ==================== 基础 Hooks 导出 ====================

// UI 相关 hooks
export { useUI, useIsMobile, useActiveTab, useSidebarOpen, useUIActions } from './index'

// 模态框相关 hooks
export {
    useModals,
    useMediaPickerModal,
    useExportModal,
    useColorPickerModal,
    useModalActions,
} from './index'

// 图片相关 hooks
export {
    useImages,
    useImageItems,
    useSelectedImageId,
    useDragState,
    useCurrentSelectedDevice,
    useImageActions,
} from './index'

// 设备相关 hooks
export { useDevices, useSelectedDeviceIndex, useDeviceActions } from './index'

// 背景相关 hooks
export {
    useBackground,
    useBackgroundType,
    useBackgroundColor,
    useMagicBackground,
    useBackgroundActions,
} from './index'

// 场景相关 hooks
export { useScene, useSceneType, useShadowType, useMockupShadow, useSceneActions } from './index'

// 组合 hooks
export { useAppState, useImageDeviceState } from './index'

// 工具函数
export { getAppState, subscribeToStore, resetAppState } from './index'

// ==================== 扩展组合 Hooks ====================

import { useUnifiedStore } from './index'
import type { UnifiedAppState } from './types'

/**
 * 获取图片和模态框的组合状态
 * 常用于图片选择相关的组件
 */
export const useImageModal = () => {
    return useUnifiedStore(state => ({
        // 图片状态
        images: state.images.images,
        selectedImageId: state.images.selectedImageId,

        // 模态框状态
        mediaPickerModal: state.modals.mediaPickerModal,

        // 操作方法
        selectImage: state.selectImage,
        openMediaPicker: state.openMediaPicker,
        closeMediaPicker: state.closeMediaPicker,
    }))
}

/**
 * 获取拖拽相关的组合状态
 * 常用于支持拖拽的组件
 */
export const useDragAndDrop = () => {
    return useUnifiedStore(state => ({
        // 拖拽状态
        dragState: state.images.dragState,

        // 操作方法
        updateDragState: state.updateDragState,
        addImages: state.addImages,
        setImageDevice: state.setImageDevice,
    }))
}

/**
 * 获取背景和场景的组合状态
 * 常用于样式设置相关的组件
 */
export const useStyleSettings = () => {
    return useUnifiedStore(state => ({
        // 背景状态
        backgroundType: state.background.backgroundType,
        backgroundColor: state.background.color,
        magicBackground: state.background.magicBackground,

        // 场景状态
        sceneType: state.scene.sceneType,
        shadowType: state.scene.shadowType,
        shadowOpacity: state.scene.shadowOpacity,

        // 操作方法
        setBackgroundType: state.setBackgroundType,
        setBackgroundColor: state.setBackgroundColor,
        setSceneType: state.setSceneType,
        setShadowOpacity: state.setShadowOpacity,
    }))
}

/**
 * 获取设备和图片绑定的组合状态
 * 常用于设备管理相关的组件
 */
export const useDeviceBinding = () => {
    return useUnifiedStore(state => ({
        // 设备状态
        selectedDeviceIndex: state.devices.selectedDeviceIndex,

        // 图片状态
        images: state.images.images,
        currentSelectedDevice: state.images.currentSelectedDevice,

        // 操作方法
        selectDevice: state.selectDevice,
        setImageDevice: state.setImageDevice,
        getImageForDevice: state.getImageForDevice,
        setCurrentSelectedDevice: state.setCurrentSelectedDevice,
    }))
}

/**
 * 获取 UI 响应式状态
 * 常用于需要响应式布局的组件
 */
export const useResponsive = () => {
    return useUnifiedStore(state => ({
        // UI 状态
        isMobile: state.ui.isMobile,
        sidebarOpen: state.ui.sidebarOpen,
        activeTab: state.ui.activeTab,

        // 操作方法
        setIsMobile: state.setIsMobile,
        toggleSidebar: state.toggleSidebar,
        setActiveTab: state.setActiveTab,
    }))
}

// ==================== 选择器 Hooks ====================

/**
 * 获取图片数量
 */
export const useImageCount = () => {
    return useUnifiedStore(state => state.images.images.length)
}

/**
 * 检查是否有图片
 */
export const useHasImages = () => {
    return useUnifiedStore(state => state.images.images.length > 0)
}

/**
 * 获取当前选中的图片对象
 */
export const useSelectedImage = () => {
    return useUnifiedStore(state => {
        const { selectedImageId, images } = state.images
        return selectedImageId ? images.find(item => item.id === selectedImageId) : null
    })
}

/**
 * 检查是否有任何模态框打开
 */
export const useHasOpenModal = () => {
    return useUnifiedStore(state => {
        return Object.values(state.modals).some(isOpen => isOpen)
    })
}

/**
 * 获取指定设备的图片
 */
export const useDeviceImage = (deviceIndex: number) => {
    return useUnifiedStore(state => {
        // 与原有逻辑一致：查找 deviceIndexes 包含指定设备索引的图片
        return (
            state.images.images.find(
                img => img.deviceIndexes && img.deviceIndexes.includes(deviceIndex),
            ) || null
        )
    })
}

/**
 * 检查设备是否有图片
 */
export const useDeviceHasImage = (deviceIndex: number) => {
    return useUnifiedStore(state => {
        // 与原有逻辑一致：检查是否有图片的 deviceIndexes 包含指定设备索引
        return state.images.images.some(
            img => img.deviceIndexes && img.deviceIndexes.includes(deviceIndex),
        )
    })
}

/**
 * 获取拖拽目标状态
 */
export const useDragTarget = (targetId: string) => {
    return useUnifiedStore(state => {
        return state.images.dragState.dragOverTarget === targetId
    })
}

/**
 * 检查是否正在拖拽
 */
export const useIsDragging = () => {
    return useUnifiedStore(state => state.images.dragState.isDragging)
}

// ==================== 计算属性 Hooks ====================

/**
 * 获取可用的设备索引列表
 */
export const useAvailableDevices = () => {
    return useUnifiedStore(state => {
        const maxDevices = 3 // 可以从配置中获取
        const usedDevices = Object.keys(state.devices.deviceImageBindings).map(Number)

        return Array.from({ length: maxDevices }, (_, index) => index).filter(
            index => !usedDevices.includes(index),
        )
    })
}

/**
 * 获取背景样式对象
 */
export const useBackgroundStyle = () => {
    return useUnifiedStore(state => {
        const { backgroundType, color, magicBackground, customImage, unsplashImage } =
            state.background

        switch (backgroundType) {
            case 'COLOR':
                return { backgroundColor: color }
            case 'MAGIC':
                return magicBackground?.value
                    ? { background: (magicBackground.value as any).background }
                    : {}
            case 'IMAGE':
                return customImage
                    ? {
                          backgroundImage: `url(${customImage.previewUrl})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                      }
                    : {}
            case 'UNSPLASH':
                return unsplashImage
                    ? {
                          backgroundImage: `url(${unsplashImage.urls.regular})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                      }
                    : {}
            default:
                return {}
        }
    })
}

/**
 * 获取阴影样式对象
 */
export const useShadowStyle = () => {
    return useUnifiedStore(state => {
        const { mockupShadow } = state.scene
        const { type: shadowType, opacity, position } = mockupShadow

        if (shadowType === 'None') {
            return {}
        }

        return {
            boxShadow: `${position.x}px ${position.y}px 20px rgba(0, 0, 0, ${opacity / 100})`,
        }
    })
}
