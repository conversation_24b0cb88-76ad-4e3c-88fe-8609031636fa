/**
 * @fileoverview 模型状态分片
 * @description 管理模型样式相关状态
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, MockupState } from '../types'
import { MockupStyleEnum } from '../../../hooks/useMockupStore'

// ==================== 模型状态分片接口 ====================

export interface MockupSlice {
    mockup: MockupState

    // 模型操作方法
    setActiveStyle: (style: MockupStyleEnum) => void
    setStyles: (styles: MockupState['styles']) => void
    resetMockup: () => void
}

// ==================== 默认状态 ====================

const defaultMockupState: MockupState = {
    styles: [
        {
            label: MockupStyleEnum.Black,
            src: '/mockups/iPhone 15 Plus/styles/black.png',
            deviceSrc: '/mockups/iPhone 15 Plus/portrait/black.png',
        },
        {
            label: MockupStyleEnum.Blue,
            src: '/mockups/iPhone 15 Plus/styles/blue.png',
            deviceSrc: '/mockups/iPhone 15 Plus/portrait/blue.png',
        },
        {
            label: MockupStyleEnum.Green,
            src: '/mockups/iPhone 15 Plus/styles/green.png',
            deviceSrc: '/mockups/iPhone 15 Plus/portrait/green.png',
        },
        {
            label: MockupStyleEnum.Pink,
            src: '/mockups/iPhone 15 Plus/styles/pink.png',
            deviceSrc: '/mockups/iPhone 15 Plus/portrait/pink.png',
        },
        {
            label: MockupStyleEnum.Yellow,
            src: '/mockups/iPhone 15 Plus/styles/yellow.png',
            deviceSrc: '/mockups/iPhone 15 Plus/portrait/yellow.png',
        },
        {
            label: MockupStyleEnum.Display,
            src: '/mockups/iPhone 15 Plus/styles/display.png',
            deviceSrc: '/mockups/iPhone 15 Plus/portrait/display.png',
        },
    ],
    activeStyle: MockupStyleEnum.Black,
}

// ==================== 模型状态分片创建函数 ====================

export const createMockupSlice: StateCreator<UnifiedAppState, [], [], MockupSlice> = (
    set,
    get,
) => ({
    // 初始状态
    mockup: defaultMockupState,

    // ==================== 模型操作方法 ====================

    /**
     * 设置当前激活的样式
     * @param style 模型样式
     */
    setActiveStyle: (style: MockupStyleEnum) => {
        set(state => ({
            mockup: {
                ...state.mockup,
                activeStyle: style,
            },
        }))

        console.log(`📱 设置激活样式: ${style}`)
    },

    /**
     * 设置可用样式列表
     * @param styles 样式列表
     */
    setStyles: (styles: MockupState['styles']) => {
        set(state => ({
            mockup: {
                ...state.mockup,
                styles,
            },
        }))

        console.log(
            `📱 设置样式列表:`,
            styles.map(s => s.label),
        )
    },

    /**
     * 重置模型状态
     */
    resetMockup: () => {
        set(state => ({
            mockup: defaultMockupState,
        }))

        console.log('🔄 已重置模型状态')
    },
})
