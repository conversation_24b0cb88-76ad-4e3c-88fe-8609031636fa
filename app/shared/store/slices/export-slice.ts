/**
 * @fileoverview 导出状态分片
 * @description 管理图片导出相关状态和操作
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, ExportState } from '../types'
import {
    ImageExportFormat,
    ExportQualitySetting,
    EXPORT_QUALITY_PRESETS,
    ExportQualityKey,
} from '../../../features/viewDimensions/utils/重构/状态管理'

// ==================== 导出状态分片接口 ====================

export interface ExportSlice {
    export: ExportState

    // 导出操作方法
    setExportFormat: (format: ImageExportFormat) => void
    setExportQuality: (quality: ExportQualitySetting) => void
    setExportScale: (scale: number) => void
    setFullExportSettings: (settings: Partial<ExportState['settings']>) => void
    setExportProgress: (progress: number) => void
    setExportStatus: (status: 'idle' | 'exporting' | 'success' | 'error') => void
    setIsExporting: (isExporting: boolean) => void
    startExport: () => void
    completeExport: () => void
    resetExportProgress: () => void
    resetExportSettings: () => void
    resetExport: () => void
}

// ==================== 默认状态 ====================

const defaultExportState: ExportState = {
    settings: {
        format: ImageExportFormat.PNG,
        quality: EXPORT_QUALITY_PRESETS[ExportQualityKey.Low],
        scale: 1,
    },
    progress: 0,
    isExporting: false,
    status: 'idle',
}

// ==================== 导出状态分片创建函数 ====================

export const createExportSlice: StateCreator<UnifiedAppState, [], [], ExportSlice> = (
    set,
    get,
) => ({
    // 初始状态
    export: defaultExportState,

    // ==================== 导出操作方法 ====================

    /**
     * 设置导出格式
     * @param format 导出格式
     */
    setExportFormat: (format: ImageExportFormat) => {
        set(state => ({
            export: {
                ...state.export,
                settings: {
                    ...state.export.settings,
                    format,
                },
            },
        }))
    },

    /**
     * 设置导出质量
     * @param quality 导出质量设置
     */
    setExportQuality: (quality: ExportQualitySetting) => {
        set(state => ({
            export: {
                ...state.export,
                settings: {
                    ...state.export.settings,
                    quality,
                },
            },
        }))
    },

    /**
     * 设置导出缩放比例
     * @param scale 缩放比例
     */
    setExportScale: (scale: number) => {
        set(state => ({
            export: {
                ...state.export,
                settings: {
                    ...state.export.settings,
                    scale: Math.max(0.1, Math.min(5, scale)), // 限制缩放范围
                },
            },
        }))
    },

    /**
     * 设置完整的导出设置
     * @param settings 导出设置更新对象
     */
    setFullExportSettings: (settings: Partial<ExportState['settings']>) => {
        set(state => ({
            export: {
                ...state.export,
                settings: {
                    ...state.export.settings,
                    ...settings,
                },
            },
        }))
    },

    /**
     * 设置导出进度
     * @param progress 进度百分比 (0-100)
     */
    setExportProgress: (progress: number) => {
        set(state => ({
            export: {
                ...state.export,
                progress: Math.max(0, Math.min(100, progress)),
            },
        }))
    },

    /**
     * 设置是否正在导出
     * @param isExporting 是否正在导出
     */
    /**
     * 设置导出状态
     * @param status 导出状态
     */
    setExportStatus: (status: 'idle' | 'exporting' | 'success' | 'error') => {
        set(state => ({
            export: {
                ...state.export,
                isExporting: status === 'exporting',
            },
        }))
    },

    setIsExporting: (isExporting: boolean) => {
        set(state => ({
            export: {
                ...state.export,
                isExporting,
                // 如果开始导出，重置进度
                progress: isExporting ? 0 : state.export.progress,
            },
        }))
    },

    /**
     * 开始导出
     */
    startExport: () => {
        set(state => ({
            export: {
                ...state.export,
                isExporting: true,
                progress: 0,
            },
        }))
    },

    /**
     * 完成导出
     */
    completeExport: () => {
        set(state => ({
            export: {
                ...state.export,
                isExporting: false,
                progress: 100,
            },
        }))
    },

    /**
     * 重置导出进度
     */
    resetExportProgress: () => {
        set(state => ({
            export: {
                ...state.export,
                progress: 0,
                isExporting: false,
            },
        }))
    },

    /**
     * 重置导出设置到默认值
     */
    resetExportSettings: () => {
        set(state => ({
            export: {
                ...state.export,
                settings: defaultExportState.settings,
            },
        }))
    },

    /**
     * 重置整个导出状态
     */
    resetExport: () => {
        set(state => ({
            export: defaultExportState,
        }))
    },
})

// ==================== 工具函数 ====================

/**
 * 获取导出文件扩展名
 * @param format 导出格式
 * @returns 文件扩展名
 */
export const getExportFileExtension = (format: ImageExportFormat): string => {
    switch (format) {
        case ImageExportFormat.JPEG:
            return 'jpg'
        case ImageExportFormat.PNG:
        default:
            return 'png'
    }
}

/**
 * 生成导出文件名
 * @param settings 导出设置
 * @param dimensions 尺寸信息
 * @returns 文件名
 */
export const generateExportFilename = (
    settings: ExportState['settings'],
    dimensions: { width: number; height: number; key?: string },
): string => {
    const { format, quality } = settings
    const { width, height, key = 'wallpaper' } = dimensions

    const extension = getExportFileExtension(format)
    const qualityLabel = quality.scaleMultiplierLabel

    return `${key}_${qualityLabel}_${width}x${height}.${extension}`
}

/**
 * 检查是否为高质量导出
 * @param quality 质量设置
 * @returns 是否为高质量
 */
export const isHighQualityExport = (quality: ExportQualitySetting): boolean => {
    return quality.scaleMultiplier >= 2
}
