/**
 * @fileoverview UI状态分片
 * @description 管理UI相关状态，包括移动设备检测、标签页状态、侧边栏状态等
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, UIState } from '../types'

// ==================== UI 状态分片接口 ====================

export interface UISlice {
    ui: UIState
    
    // UI 操作方法
    setIsMobile: (isMobile: boolean) => void
    setActiveTab: (tab: string) => void
    toggleSidebar: () => void
    setSidebarOpen: (open: boolean) => void
    resetUI: () => void
}

// ==================== 默认状态 ====================

const defaultUIState: UIState = {
    isMobile: false,
    activeTab: 'mockup',
    sidebarOpen: true,
}

// ==================== UI 状态分片创建函数 ====================

export const createUISlice: StateCreator<
    UnifiedAppState,
    [],
    [],
    UISlice
> = (set, get) => ({
    // 初始状态
    ui: defaultUIState,

    // ==================== UI 操作方法 ====================

    /**
     * 设置移动设备状态
     * @param isMobile 是否为移动设备
     */
    setIsMobile: (isMobile: boolean) => {
        set((state) => ({
            ui: {
                ...state.ui,
                isMobile,
            },
        }))
    },

    /**
     * 设置当前激活的标签页
     * @param tab 标签页名称
     */
    setActiveTab: (tab: string) => {
        set((state) => ({
            ui: {
                ...state.ui,
                activeTab: tab,
            },
        }))
    },

    /**
     * 切换侧边栏显示状态
     */
    toggleSidebar: () => {
        set((state) => ({
            ui: {
                ...state.ui,
                sidebarOpen: !state.ui.sidebarOpen,
            },
        }))
    },

    /**
     * 设置侧边栏显示状态
     * @param open 是否打开侧边栏
     */
    setSidebarOpen: (open: boolean) => {
        set((state) => ({
            ui: {
                ...state.ui,
                sidebarOpen: open,
            },
        }))
    },

    /**
     * 重置UI状态到默认值
     */
    resetUI: () => {
        set((state) => ({
            ui: defaultUIState,
        }))
    },
})

// ==================== 工具函数 ====================

/**
 * 移动设备检测断点
 */
export const MOBILE_BREAKPOINT = 800

/**
 * 初始化移动设备检测
 * @returns 清理函数
 */
export const initMobileDetection = (setIsMobile: (isMobile: boolean) => void): (() => void) => {
    // 确保在客户端环境中运行
    if (typeof window === 'undefined') return () => {}

    console.log('🔍 initMobileDetection 开始执行')
    console.log('🖥️ 窗口尺寸:', window.innerWidth, 'x', window.innerHeight)
    console.log('📱 移动设备断点:', MOBILE_BREAKPOINT, 'px')

    // 初始检测
    const updateMobileState = () => {
        const mediaQuery = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT}px)`)
        const isMobile = mediaQuery.matches
        console.log('📏 媒体查询结果:', mediaQuery.media, '→', isMobile)
        console.log('📱 设置 isMobile 为:', isMobile)

        setIsMobile(isMobile)
    }

    // 立即执行一次检测
    updateMobileState()

    // 设置媒体查询监听器
    const mediaQuery = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT}px)`)
    const handleResize = (e: MediaQueryListEvent) => {
        console.log('📱 窗口尺寸改变:', e.matches ? '移动设备' : 'PC设备')
        setIsMobile(e.matches)
    }

    mediaQuery.addEventListener('change', handleResize)

    // 返回清理函数
    return () => {
        console.log('🧹 清理移动设备检测监听器')
        mediaQuery.removeEventListener('change', handleResize)
    }
}
