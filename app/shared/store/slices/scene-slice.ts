/**
 * @fileoverview 场景状态分片
 * @description 管理场景相关状态，包括场景类型、阴影设置等
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, SceneState } from '../types'
import { SceneTabType, ShadowLayerType, ShadowType } from '../../../hooks/useSceneStore'
import { ShadowTypeEnum } from '../../../hooks/useMockupShadowStore'

// ==================== 场景状态分片接口 ====================

export interface SceneSlice {
    scene: SceneState

    // 场景操作方法
    setSceneType: (type: SceneTabType) => void
    setShadowType: (type: ShadowType) => void
    setShadowOpacity: (opacity: number) => void
    setShadowLayer: (layer: ShadowLayerType) => void
    setMockupShadow: (shadow: SceneState['mockupShadow']) => void
    setMockupShadowType: (type: ShadowTypeEnum) => void
    setMockupShadowOpacity: (opacity: number) => void
    setMockupShadowPosition: (position: { x: number; y: number }) => void
    resetScene: () => void
}

// ==================== 默认状态 ====================

const defaultSceneState: SceneState = {
    sceneType: SceneTabType.NONE,
    shadowType: {
        index: 1,
        src: '/display-assets/shadow-overlays/051.png',
    },
    shadowOpacity: 40,
    shadowLayer: ShadowLayerType.OVERLAY,
    mockupShadow: {
        type: ShadowTypeEnum.Hug,
        opacity: 40,
        position: { x: 0, y: 0 },
    },
}

// ==================== 场景状态分片创建函数 ====================

export const createSceneSlice: StateCreator<UnifiedAppState, [], [], SceneSlice> = (set, get) => ({
    // 初始状态
    scene: defaultSceneState,

    // ==================== 场景操作方法 ====================

    /**
     * 设置场景类型
     * @param type 场景类型
     */
    setSceneType: (type: SceneTabType) => {
        set(state => ({
            scene: {
                ...state.scene,
                sceneType: type,
            },
        }))

        console.log(`🎬 设置场景类型: ${type}`)
    },

    /**
     * 设置阴影类型
     * @param type 阴影类型
     */
    setShadowType: (type: ShadowType) => {
        set(state => ({
            scene: {
                ...state.scene,
                shadowType: type,
            },
        }))

        console.log(`🌑 设置阴影类型: ${type}`)
    },

    /**
     * 设置阴影透明度
     * @param opacity 透明度值 (10-80，与滑块配置一致)
     */
    setShadowOpacity: (opacity: number) => {
        // 修正：使用与滑块配置一致的范围 (10-80)
        const clampedOpacity = Math.max(10, Math.min(80, opacity))

        set(state => ({
            scene: {
                ...state.scene,
                shadowOpacity: clampedOpacity,
            },
        }))

        console.warn(`🌑 设置场景阴影透明度: 输入=${opacity} -> 输出=${clampedOpacity}`)
    },

    /**
     * 设置阴影层级
     * @param layer 阴影层级
     */
    setShadowLayer: (layer: ShadowLayerType) => {
        set(state => ({
            scene: {
                ...state.scene,
                shadowLayer: layer,
            },
        }))

        console.log(`🌑 设置阴影层级: ${layer}`)
    },

    /**
     * 设置模型阴影配置
     * @param shadow 阴影配置
     */
    setMockupShadow: (shadow: SceneState['mockupShadow']) => {
        set(state => ({
            scene: {
                ...state.scene,
                mockupShadow: shadow,
            },
        }))

        console.log(`🌑 设置模型阴影:`, shadow)
    },

    /**
     * 设置模型阴影类型
     * @param type 阴影类型枚举
     */
    setMockupShadowType: (type: ShadowTypeEnum) => {
        set(state => ({
            scene: {
                ...state.scene,
                mockupShadow: {
                    ...state.scene.mockupShadow,
                    type,
                },
            },
        }))

        console.warn(`🌑 设置模型阴影类型:`, type)
    },

    /**
     * 设置模型阴影透明度
     * @param opacity 透明度值 (10-70，与滑块配置一致)
     */
    setMockupShadowOpacity: (opacity: number) => {
        // 使用与滑块配置一致的范围 (10-70)
        const clampedOpacity = Math.max(10, Math.min(70, opacity))

        set(state => ({
            scene: {
                ...state.scene,
                mockupShadow: {
                    ...state.scene.mockupShadow,
                    opacity: clampedOpacity,
                },
            },
        }))

        console.warn(`🌑 设置模型阴影透明度: 输入=${opacity} -> 输出=${clampedOpacity}`)
    },

    /**
     * 设置模型阴影位置
     * @param position 阴影位置
     */
    setMockupShadowPosition: (position: { x: number; y: number }) => {
        set(state => ({
            scene: {
                ...state.scene,
                mockupShadow: {
                    ...state.scene.mockupShadow,
                    position,
                },
            },
        }))

        console.warn(`🌑 设置模型阴影位置:`, position)
    },

    /**
     * 重置场景状态
     */
    resetScene: () => {
        set(state => ({
            scene: defaultSceneState,
        }))

        console.log('🔄 已重置场景状态')
    },
})
