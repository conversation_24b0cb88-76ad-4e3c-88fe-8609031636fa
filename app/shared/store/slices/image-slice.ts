/**
 * @fileoverview 图片状态分片
 * @description 管理图片相关状态，包括图片列表、选择、拖拽、上传等
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, ImageState } from '../types'
import { ImageItem, DragState, processFiles } from '../../../ImageMange/imageMangeIndex'

// ==================== 图片状态分片接口 ====================

export interface ImageSlice {
    images: ImageState

    // 图片操作方法
    addImages: (files: File[]) => Promise<void>
    removeImage: (id: string) => void
    removeImages: (ids: string[]) => void
    selectImage: (id: string | null) => void
    updateDragState: (dragState: Partial<DragState>) => void
    updateImageStatus: (
        id: string,
        status: import('../../../ImageMange/imageMangeIndex').ImageStatus,
    ) => void
    setImageDevice: (imageId: string, deviceIndex: number) => void
    unbindImageFromDevice: (imageId: string, deviceIndex: number) => void
    removeImageFromDevice: (imageId: string, deviceIndex: number) => void
    getImageForDevice: (deviceIndex: number) => ImageItem | null
    setCurrentSelectedDevice: (deviceIndex: number | null) => void
    selectImageForCurrentDevice: (imageId: string) => void
    getImagesByDevice: (deviceIndex?: number) => ImageItem[]
    getNextAvailableDevice: () => number
    isImageBindToDevice: (imageId: string, deviceIndex: number) => boolean
    updateUploadProgress: (imageId: string, progress: number) => void
    clearAllImages: () => void
    resetImages: () => void
}

// ==================== 默认状态 ====================

const defaultImageState: ImageState = {
    images: [],
    selectedImageId: null,
    dragState: {
        isDragging: false,
        dragOverTarget: null,
        dropPosition: null,
    },
    uploadProgress: {},
    currentSelectedDevice: null,
}

// ==================== 图片状态分片创建函数 ====================

export const createImageSlice: StateCreator<UnifiedAppState, [], [], ImageSlice> = (set, get) => ({
    // 初始状态
    images: defaultImageState,

    // ==================== 图片操作方法 ====================

    /**
     * 添加图片到系统
     * @param files 要添加的文件列表
     */
    addImages: async (files: File[]) => {
        console.log(`🚀 开始处理 ${files.length} 个文件`)

        try {
            // 异步处理所有文件
            const processedItems = await processFiles(files)

            if (processedItems.length > 0) {
                const currentState = get()

                // 先将图片添加到存储中
                set(state => ({
                    images: {
                        ...state.images,
                        images: [...state.images.images, ...processedItems],
                    },
                }))

                // 智能绑定：如果有选中设备，自动绑定第一张图片
                if (
                    currentState.images.currentSelectedDevice !== null &&
                    processedItems.length > 0
                ) {
                    // 直接调用setImageDevice方法
                    set(state => ({
                        images: {
                            ...state.images,
                            images: state.images.images.map(img => {
                                if (img.id === processedItems[0].id) {
                                    const deviceIndexes = img.deviceIndexes || []
                                    if (
                                        !deviceIndexes.includes(
                                            currentState.images.currentSelectedDevice!,
                                        )
                                    ) {
                                        return {
                                            ...img,
                                            deviceIndexes: [
                                                ...deviceIndexes,
                                                currentState.images.currentSelectedDevice!,
                                            ],
                                        }
                                    }
                                }
                                return img
                            }),
                        },
                    }))
                }
            } else {
                console.warn('⚠️ 没有有效的图片文件被处理')
            }
        } catch (error) {
            console.error('❌ 添加图片时发生错误:', error)
        }
    },

    /**
     * 删除单张图片
     * @param id 图片ID
     */
    removeImage: (id: string) => {
        set(state => {
            const updatedImages = state.images.images.filter(img => img.id !== id)

            return {
                images: {
                    ...state.images,
                    images: updatedImages,
                    selectedImageId:
                        state.images.selectedImageId === id ? null : state.images.selectedImageId,
                    uploadProgress: Object.fromEntries(
                        Object.entries(state.images.uploadProgress).filter(
                            ([imgId]) => imgId !== id,
                        ),
                    ),
                },
            }
        })

        console.log(`🗑️ 已删除图片: ${id}`)
    },

    /**
     * 删除多张图片
     * @param ids 图片ID列表
     */
    removeImages: (ids: string[]) => {
        set(state => {
            const updatedImages = state.images.images.filter(img => !ids.includes(img.id))
            const newSelectedId = ids.includes(state.images.selectedImageId || '')
                ? null
                : state.images.selectedImageId

            return {
                images: {
                    ...state.images,
                    images: updatedImages,
                    selectedImageId: newSelectedId,
                    uploadProgress: Object.fromEntries(
                        Object.entries(state.images.uploadProgress).filter(
                            ([imgId]) => !ids.includes(imgId),
                        ),
                    ),
                },
            }
        })

        console.log(`🗑️ 已删除 ${ids.length} 张图片`)
    },

    /**
     * 选择图片
     * @param id 图片ID，null表示取消选择
     */
    selectImage: (id: string | null) => {
        set(state => ({
            images: {
                ...state.images,
                selectedImageId: id,
            },
        }))

        console.log(`🖼️ 选择图片: ${id || '无'}`)
    },

    /**
     * 更新拖拽状态
     * @param dragState 拖拽状态更新
     */
    updateDragState: (dragState: Partial<DragState>) => {
        set(state => ({
            images: {
                ...state.images,
                dragState: {
                    ...state.images.dragState,
                    ...dragState,
                },
            },
        }))
    },

    /**
     * 设置图片绑定到设备
     * @param imageId 图片ID
     * @param deviceIndex 设备索引
     */
    setImageDevice: (imageId: string, deviceIndex: number) => {
        set(state => {
            const updatedImages = state.images.images.map(img => {
                if (img.id === imageId) {
                    // 添加设备绑定
                    const deviceIndexes = img.deviceIndexes || []
                    if (!deviceIndexes.includes(deviceIndex)) {
                        return {
                            ...img,
                            deviceIndexes: [...deviceIndexes, deviceIndex],
                        }
                    }
                } else {
                    // 从其他图片中移除该设备的绑定（保证设备唯一性）
                    const deviceIndexes = img.deviceIndexes || []
                    if (deviceIndexes.includes(deviceIndex)) {
                        return {
                            ...img,
                            deviceIndexes: deviceIndexes.filter(idx => idx !== deviceIndex),
                        }
                    }
                }
                return img
            })

            return {
                images: {
                    ...state.images,
                    images: updatedImages,
                },
            }
        })

        console.log(`🔗 绑定图片 ${imageId} 到设备 ${deviceIndex}`)
    },

    /**
     * 解除图片与设备的绑定
     * @param imageId 图片ID
     * @param deviceIndex 设备索引
     */
    unbindImageFromDevice: (imageId: string, deviceIndex: number) => {
        set(state => {
            const updatedImages = state.images.images.map(img => {
                if (img.id === imageId) {
                    const deviceIndexes = img.deviceIndexes || []
                    return {
                        ...img,
                        deviceIndexes: deviceIndexes.filter(idx => idx !== deviceIndex),
                    }
                }
                return img
            })

            return {
                images: {
                    ...state.images,
                    images: updatedImages,
                },
            }
        })

        console.log(`🔓 解除图片 ${imageId} 与设备 ${deviceIndex} 的绑定`)
    },

    /**
     * 获取绑定到指定设备的图片
     * @param deviceIndex 设备索引
     * @returns 绑定的图片或null
     */
    getImageForDevice: (deviceIndex: number): ImageItem | null => {
        const state = get()
        return state.images.images.find(img => img.deviceIndexes?.includes(deviceIndex)) || null
    },

    /**
     * 设置当前选中的设备
     * @param deviceIndex 设备索引
     */
    setCurrentSelectedDevice: (deviceIndex: number | null) => {
        set(state => ({
            images: {
                ...state.images,
                currentSelectedDevice: deviceIndex,
            },
        }))

        console.log(`📱 设置当前选中设备: ${deviceIndex}`)
    },

    /**
     * 更新图片上传进度
     * @param imageId 图片ID
     * @param progress 进度百分比
     */
    updateUploadProgress: (imageId: string, progress: number) => {
        set(state => ({
            images: {
                ...state.images,
                uploadProgress: {
                    ...state.images.uploadProgress,
                    [imageId]: Math.max(0, Math.min(100, progress)),
                },
            },
        }))
    },

    /**
     * 清空所有图片
     */
    clearAllImages: () => {
        set(state => ({
            images: {
                ...defaultImageState,
                currentSelectedDevice: state.images.currentSelectedDevice, // 保留当前选中设备
            },
        }))

        console.log('🧹 已清空所有图片')
    },

    /**
     * 重置图片状态
     */
    resetImages: () => {
        set(state => ({
            images: defaultImageState,
        }))

        console.log('🔄 已重置图片状态')
    },

    /**
     * 更新图片状态
     * @param id 图片ID
     * @param status 新的状态
     */
    updateImageStatus: (
        id: string,
        status: import('../../../ImageMange/imageMangeIndex').ImageStatus,
    ) => {
        set(state => ({
            images: {
                ...state.images,
                images: state.images.images.map(img => (img.id === id ? { ...img, status } : img)),
            },
        }))
        console.log(`📊 图片状态更新: ${id} → ${status}`)
    },

    /**
     * 从设备解绑图片（别名方法，与unbindImageFromDevice相同）
     * @param imageId 图片ID
     * @param deviceIndex 设备索引
     */
    removeImageFromDevice: (imageId: string, deviceIndex: number) => {
        set(state => {
            const updatedImages = state.images.images.map(img => {
                if (img.id === imageId) {
                    const deviceIndexes = img.deviceIndexes || []
                    return {
                        ...img,
                        deviceIndexes: deviceIndexes.filter(idx => idx !== deviceIndex),
                    }
                }
                return img
            })

            return {
                images: {
                    ...state.images,
                    images: updatedImages,
                },
            }
        })

        console.log(`🔓 解绑完成: 图片 ${imageId} 从设备 ${deviceIndex} 解绑`)
    },

    /**
     * 将指定图片绑定到当前选中的设备
     * @param imageId 要绑定的图片ID
     */
    selectImageForCurrentDevice: (imageId: string) => {
        const state = get()
        const { currentSelectedDevice } = state.images

        // 检查前置条件：必须有选中的设备
        if (currentSelectedDevice === null) {
            console.warn('⚠️ 没有选中的设备，无法进行图片绑定操作')
            return
        }

        // 验证图片存在性
        const targetImage = state.images.images.find(img => img.id === imageId)
        if (!targetImage) {
            console.warn(`⚠️ 图片不存在: ${imageId}`)
            return
        }

        // 执行智能绑定 - 复制setImageDevice的逻辑
        set(state => ({
            images: {
                ...state.images,
                images: state.images.images.map(img => {
                    if (img.id === imageId) {
                        // 添加设备绑定
                        const deviceIndexes = img.deviceIndexes || []
                        if (!deviceIndexes.includes(currentSelectedDevice)) {
                            return {
                                ...img,
                                deviceIndexes: [...deviceIndexes, currentSelectedDevice],
                            }
                        }
                    } else {
                        // 从其他图片中移除该设备的绑定（保证设备唯一性）
                        const deviceIndexes = img.deviceIndexes || []
                        if (deviceIndexes.includes(currentSelectedDevice)) {
                            return {
                                ...img,
                                deviceIndexes: deviceIndexes.filter(
                                    idx => idx !== currentSelectedDevice,
                                ),
                            }
                        }
                    }
                    return img
                }),
            },
        }))

        console.log(`🎯 图片选择绑定: ${imageId} → 设备 ${currentSelectedDevice}`)
    },

    /**
     * 查询指定设备的图片列表
     * @param deviceIndex 设备索引，可选
     * @returns 匹配条件的图片列表
     */
    getImagesByDevice: (deviceIndex?: number) => {
        const state = get()
        const { images } = state.images

        if (deviceIndex === undefined) {
            // 返回未绑定任何设备的图片
            return images.filter(img => !img.deviceIndexes || img.deviceIndexes.length === 0)
        }

        // 返回绑定到指定设备的图片
        return images.filter(img => img.deviceIndexes && img.deviceIndexes.includes(deviceIndex))
    },

    /**
     * 获取下一个可用的设备索引
     * @returns 下一个可用的设备索引
     */
    getNextAvailableDevice: () => {
        const state = get()
        const { images } = state.images
        const usedDevices = new Set<number>()

        // 收集所有已被使用的设备索引
        images.forEach(img => {
            if (img.deviceIndexes) {
                img.deviceIndexes.forEach(idx => usedDevices.add(idx))
            }
        })

        // 从0开始找第一个未使用的设备索引
        let deviceIndex = 0
        while (usedDevices.has(deviceIndex)) {
            deviceIndex++
        }

        return deviceIndex
    },

    /**
     * 检查图片是否绑定到指定设备
     * @param imageId 图片ID
     * @param deviceIndex 设备索引
     * @returns 是否绑定
     */
    isImageBindToDevice: (imageId: string, deviceIndex: number) => {
        const state = get()
        const image = state.images.images.find(img => img.id === imageId)
        return image?.deviceIndexes?.includes(deviceIndex) || false
    },
})
