/**
 * @fileoverview 背景状态分片
 * @description 管理背景相关状态，包括背景类型、颜色、图片等
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, BackgroundState } from '../types'
import { BackgroundTypeEnum } from '../../../hooks/useBackgroundStore'

// ==================== 背景状态分片接口 ====================

export interface BackgroundSlice {
    background: BackgroundState

    // 背景操作方法
    setBackgroundType: (type: BackgroundTypeEnum) => void
    setBackgroundColor: (color: string) => void
    setColor: (newColor: string) => void
    setUnsplashImage: (image: { url: string; author: string } | null) => void
    setCustomImage: (image: { file: File; preview: string } | null) => void
    setColorPickerVisible: (visible: boolean) => void
    setImagePickerVisible: (visible: boolean) => void
    toggleColorPicker: (visible?: boolean) => void
    toggleImagePicker: (visible?: boolean) => void
    setTransparent: () => void
    setImageBackground: () => void
    setMagicBackgroundType: () => void
    setMagicBackground: (config: BackgroundState['magicBackground']) => void
    resetBackground: () => void
}

// ==================== 默认状态 ====================

const defaultBackgroundState: BackgroundState = {
    backgroundType: BackgroundTypeEnum.MAGIC,
    color: '',
    unsplashImage: null,
    customImage: null,
    isColorPickerVisible: false,
    isImagePickerVisible: false,
    magicBackground: {
        type: 'gradient',
        value: {
            background:
                'linear-gradient(140deg, rgb(255, 100, 50) 12.8%, rgb(255, 0, 101) 43.52%, rgb(123, 46, 255) 84.34%)',
        },
    },
}

// ==================== 背景状态分片创建函数 ====================

export const createBackgroundSlice: StateCreator<UnifiedAppState, [], [], BackgroundSlice> = (
    set,
    get,
) => ({
    // 初始状态
    background: defaultBackgroundState,

    // ==================== 背景操作方法 ====================

    /**
     * 设置背景类型
     * @param type 背景类型
     */
    setBackgroundType: (type: BackgroundTypeEnum) => {
        set(state => ({
            background: {
                ...state.background,
                backgroundType: type,
            },
        }))

        console.log(`🎨 设置背景类型: ${type}`)
    },

    /**
     * 设置背景颜色
     * @param color 颜色值
     */
    setBackgroundColor: (color: string) => {
        set(state => ({
            background: {
                ...state.background,
                color,
                magicBackground: {
                    ...state.background.magicBackground,
                    value: {
                        ...state.background.magicBackground.value,
                        background: color,
                    },
                },
            },
        }))

        console.log(`🎨 设置背景颜色: ${color}`)
    },

    /**
     * 设置Unsplash图片
     * @param image 图片信息
     */
    setUnsplashImage: (image: { url: string; author: string } | null) => {
        set(state => ({
            background: {
                ...state.background,
                unsplashImage: image,
            },
        }))

        console.log('🖼️ 设置Unsplash图片:', image)
    },

    /**
     * 设置自定义图片
     * @param image 图片信息
     */
    setCustomImage: (image: { file: File; preview: string } | null) => {
        set(state => ({
            background: {
                ...state.background,
                customImage: image,
            },
        }))

        console.log('🖼️ 设置自定义图片:', image?.file.name)
    },

    /**
     * 设置颜色选择器可见性
     * @param visible 是否可见
     */
    setColorPickerVisible: (visible: boolean) => {
        set(state => ({
            background: {
                ...state.background,
                isColorPickerVisible: visible,
            },
        }))

        console.log(`🎨 设置颜色选择器可见性: ${visible}`)
    },

    /**
     * 设置图片选择器可见性
     * @param visible 是否可见
     */
    setImagePickerVisible: (visible: boolean) => {
        set(state => ({
            background: {
                ...state.background,
                isImagePickerVisible: visible,
            },
        }))

        console.log(`🖼️ 设置图片选择器可见性: ${visible}`)
    },

    /**
     * 设置魔法背景配置
     * @param config 魔法背景配置
     */
    setMagicBackground: (config: BackgroundState['magicBackground']) => {
        set(state => ({
            background: {
                ...state.background,
                magicBackground: config,
            },
        }))

        console.log('✨ 设置魔法背景:', config)
    },

    /**
     * 重置背景状态
     */
    resetBackground: () => {
        set(state => ({
            background: defaultBackgroundState,
        }))

        console.log('🔄 已重置背景状态')
    },

    /**
     * 设置背景颜色（别名方法）
     * @param newColor 颜色值
     */
    setColor: (newColor: string) => {
        set(state => ({
            background: {
                ...state.background,
                color: newColor,
                backgroundType: BackgroundTypeEnum.COLOR,
                magicBackground: {
                    ...state.background.magicBackground,
                    value: {
                        ...state.background.magicBackground.value,
                        background: newColor,
                    },
                },
            },
        }))

        console.log(`🎨 设置背景颜色: ${newColor}`)
    },

    /**
     * 切换颜色选择器可见性
     * @param visible 是否可见，不传则取反
     */
    toggleColorPicker: (visible?: boolean) => {
        set(state => ({
            background: {
                ...state.background,
                isColorPickerVisible: visible ?? !state.background.isColorPickerVisible,
            },
        }))

        console.log('🎨 切换颜色选择器可见性')
    },

    /**
     * 切换图片选择器可见性
     * @param visible 是否可见，不传则取反
     */
    toggleImagePicker: (visible?: boolean) => {
        set(state => ({
            background: {
                ...state.background,
                isImagePickerVisible: visible ?? !state.background.isImagePickerVisible,
            },
        }))

        console.log('🖼️ 切换图片选择器可见性')
    },

    /**
     * 设置为透明背景
     */
    setTransparent: () => {
        set(state => ({
            background: {
                ...state.background,
                backgroundType: BackgroundTypeEnum.TRANSPARENT,
            },
        }))

        console.log('🎨 设置透明背景')
    },

    /**
     * 设置为图片背景
     */
    setImageBackground: () => {
        set(state => ({
            background: {
                ...state.background,
                backgroundType: BackgroundTypeEnum.IMAGE,
            },
        }))

        console.log('🖼️ 设置图片背景')
    },

    /**
     * 设置为魔法背景
     */
    setMagicBackgroundType: () => {
        set(state => ({
            background: {
                ...state.background,
                backgroundType: BackgroundTypeEnum.MAGIC,
            },
        }))

        console.log('✨ 设置魔法背景')
    },
})
