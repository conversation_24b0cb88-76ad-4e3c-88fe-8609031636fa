/**
 * @fileoverview 模态框状态分片
 * @description 管理所有模态框的显示状态和操作
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, ModalState } from '../types'

// ==================== 模态框键枚举 ====================

export enum ModalKey {
    MediaPickerModal = 'mediaPickerModal',
    ExportPopover = 'exportPopover',
    SuccessPopover = 'successPopover',
    ColorPickerModal = 'colorPickerModal',
    ImagePickerModal = 'imagePickerModal',
}

// ==================== 模态框状态分片接口 ====================

export interface ModalSlice {
    modals: ModalState

    // 模态框操作方法
    setModalState: (modalName: keyof ModalState, isOpen: boolean) => void
    toggleModal: (modalName: keyof ModalState) => void
    closeAllModals: () => void
    openMediaPicker: () => void
    closeMediaPicker: () => void
    openExportModal: () => void
    closeExportModal: () => void
    showSuccessModal: () => void
    resetModals: () => void
}

// ==================== 默认状态 ====================

const defaultModalState: ModalState = {
    mediaPickerModal: false,
    exportPopover: false,
    successPopover: false,
    colorPickerModal: false,
    imagePickerModal: false,
}

// ==================== 模态框状态分片创建函数 ====================

export const createModalSlice: StateCreator<UnifiedAppState, [], [], ModalSlice> = (set, get) => ({
    // 初始状态
    modals: defaultModalState,

    // ==================== 模态框操作方法 ====================

    /**
     * 设置指定模态框的显示状态
     * @param modalName 模态框名称
     * @param isOpen 是否显示
     */
    setModalState: (modalName: keyof ModalState, isOpen: boolean) => {
        set(state => ({
            modals: {
                ...state.modals,
                [modalName]: isOpen,
            },
        }))
    },

    /**
     * 切换指定模态框的显示状态
     * @param modalName 模态框名称
     */
    toggleModal: (modalName: keyof ModalState) => {
        set(state => ({
            modals: {
                ...state.modals,
                [modalName]: !state.modals[modalName],
            },
        }))
    },

    /**
     * 关闭所有模态框
     */
    closeAllModals: () => {
        set(state => ({
            modals: defaultModalState,
        }))
    },

    /**
     * 打开媒体选择器
     */
    openMediaPicker: () => {
        set(state => ({
            modals: {
                ...state.modals,
                mediaPickerModal: true,
            },
        }))
    },

    /**
     * 关闭媒体选择器
     */
    closeMediaPicker: () => {
        set(state => ({
            modals: {
                ...state.modals,
                mediaPickerModal: false,
            },
        }))
    },

    /**
     * 打开导出模态框
     */
    openExportModal: () => {
        set(state => ({
            modals: {
                ...state.modals,
                exportPopover: true,
            },
        }))
    },

    /**
     * 关闭导出模态框
     */
    closeExportModal: () => {
        set(state => ({
            modals: {
                ...state.modals,
                exportPopover: false,
            },
        }))
    },

    /**
     * 显示成功提示模态框
     * 同时关闭导出模态框
     */
    showSuccessModal: () => {
        set(state => ({
            modals: {
                ...state.modals,
                exportPopover: false,
                successPopover: true,
            },
        }))
    },

    /**
     * 重置所有模态框状态
     */
    resetModals: () => {
        set(state => ({
            modals: defaultModalState,
        }))
    },
})

// ==================== 工具函数 ====================

/**
 * 检查是否有任何模态框打开
 * @param modals 模态框状态
 * @returns 是否有模态框打开
 */
export const hasAnyModalOpen = (modals: ModalState): boolean => {
    return Object.values(modals).some(isOpen => isOpen)
}

/**
 * 获取当前打开的模态框列表
 * @param modals 模态框状态
 * @returns 打开的模态框键列表
 */
export const getOpenModals = (modals: ModalState): ModalKey[] => {
    return Object.entries(modals)
        .filter(([_, isOpen]) => isOpen)
        .map(([key, _]) => key as ModalKey)
}
