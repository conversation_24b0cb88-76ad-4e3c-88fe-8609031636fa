/**
 * @fileoverview 画布状态分片
 * @description 管理画布和视图相关状态，包括尺寸、布局、缩放等
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, CanvasState } from '../types'
import { sizeInfo, viewSizes } from '../../../features/viewDimensions/utils/重构/dimensions'
import { DimensionSourceType } from '../../../features/viewDimensions/utils/重构/状态管理'
import { calculateDimensions } from '../../../features/viewDimensions/utils/重构/算法'
import { getAvailableSpace } from '../../../features/viewDimensions/utils/重构/获取'

// ==================== 画布状态分片接口 ====================

export interface CanvasSlice {
    canvas: CanvasState

    // 画布操作方法
    setDimensions: (dimensions: CanvasState['dimensions']) => void
    setViewMode: (mode: 'preview' | 'edit') => void
    setActiveLayout: (layout: CanvasState['activeLayout']) => void
    setScale: (scale: number) => void
    resetToDefaultDimensions: () => void
    calculateAndUpdateViewDimensions: () => void
    setViewDimensionsWithCalculation: (dimensionsUpdate: Partial<CanvasState['dimensions']>) => void
    resetCanvas: () => void
}

// ==================== 默认状态 ====================

const initialDefaultViewDimensions: sizeInfo = viewSizes[0].sizes[2] // 默认视图尺寸1920x1440

const defaultCanvasState: CanvasState = {
    dimensions: {
        key: initialDefaultViewDimensions.key,
        useWidth: initialDefaultViewDimensions.useWidth,
        useHeight: initialDefaultViewDimensions.useHeight,
        ratioWidth: initialDefaultViewDimensions.ratioWidth,
        ratioHeight: initialDefaultViewDimensions.ratioHeight,
        sourceType: DimensionSourceType.Default,
        calculatedWidth: undefined,
        calculatedHeight: undefined,
        calculationMethod: undefined,
    },
    viewMode: 'preview',
    activeLayout: {
        type: 'single',
        id: 0,
    },
    scale: 1,
}

// ==================== 画布状态分片创建函数 ====================

export const createCanvasSlice: StateCreator<UnifiedAppState, [], [], CanvasSlice> = (
    set,
    get,
) => ({
    // 初始状态
    canvas: defaultCanvasState,

    // ==================== 画布操作方法 ====================

    /**
     * 设置视图尺寸
     * @param dimensions 完整的尺寸对象
     */
    setDimensions: (dimensions: CanvasState['dimensions']) => {
        set(state => ({
            canvas: {
                ...state.canvas,
                dimensions,
            },
        }))
    },

    /**
     * 设置视图尺寸（部分更新）
     * @param dimensionsUpdate 尺寸更新对象
     */
    setViewDimensions: (dimensionsUpdate: Partial<CanvasState['dimensions']>) => {
        set(state => ({
            canvas: {
                ...state.canvas,
                dimensions: {
                    ...state.canvas.dimensions,
                    ...dimensionsUpdate,
                },
            },
        }))
    },

    /**
     * 设置视图模式
     * @param mode 视图模式
     */
    setViewMode: (mode: 'preview' | 'edit') => {
        set(state => ({
            canvas: {
                ...state.canvas,
                viewMode: mode,
            },
        }))
    },

    /**
     * 设置当前激活的布局
     * @param layout 布局配置
     */
    setActiveLayout: (layout: { type: 'single' | 'dual' | 'triple'; id: number }) => {
        set(state => ({
            canvas: {
                ...state.canvas,
                activeLayout: layout,
            },
        }))
    },

    /**
     * 设置缩放比例
     * @param scale 缩放比例
     */
    setScale: (scale: number) => {
        set(state => ({
            canvas: {
                ...state.canvas,
                scale: Math.max(0.1, Math.min(5, scale)), // 限制缩放范围
            },
        }))
    },

    /**
     * 重置为默认尺寸
     */
    resetToDefaultDimensions: () => {
        set(state => ({
            canvas: {
                ...state.canvas,
                dimensions: {
                    key: initialDefaultViewDimensions.key,
                    useWidth: initialDefaultViewDimensions.useWidth,
                    useHeight: initialDefaultViewDimensions.useHeight,
                    ratioWidth: initialDefaultViewDimensions.ratioWidth,
                    ratioHeight: initialDefaultViewDimensions.ratioHeight,
                    sourceType: DimensionSourceType.Default,
                    calculatedWidth: undefined,
                    calculatedHeight: undefined,
                    calculationMethod: undefined,
                },
            },
        }))
    },

    /**
     * 计算并更新视图尺寸
     */
    calculateAndUpdateViewDimensions: () => {
        const state = get()
        const currentDimensions = state.canvas.dimensions

        try {
            // 获取可用空间
            const availableSpace = getAvailableSpace()
            if (!availableSpace) {
                console.warn('无法获取可用空间信息')
                return
            }

            // 计算尺寸
            const calculationResult = calculateDimensions(currentDimensions, availableSpace)

            if (calculationResult) {
                set(state => ({
                    canvas: {
                        ...state.canvas,
                        dimensions: {
                            ...state.canvas.dimensions,
                            calculatedWidth: calculationResult.width,
                            calculatedHeight: calculationResult.height,
                            calculationMethod: calculationResult.method,
                        },
                    },
                }))
            }
        } catch (error) {
            console.error('计算视图尺寸时发生错误:', error)
        }
    },

    /**
     * 设置视图尺寸并自动计算显示尺寸
     * @param dimensionsUpdate 尺寸更新对象
     */
    setViewDimensionsWithCalculation: (dimensionsUpdate: Partial<CanvasState['dimensions']>) => {
        // 先更新尺寸
        set(state => ({
            canvas: {
                ...state.canvas,
                dimensions: {
                    ...state.canvas.dimensions,
                    ...dimensionsUpdate,
                },
            },
        }))

        // 然后计算显示尺寸
        get().calculateAndUpdateViewDimensions()
    },

    /**
     * 重置画布状态
     */
    resetCanvas: () => {
        set(state => ({
            canvas: defaultCanvasState,
        }))
    },
})

// ==================== 工具函数 ====================

/**
 * 检查尺寸是否为默认尺寸
 * @param dimensions 当前尺寸
 * @returns 是否为默认尺寸
 */
export const isDefaultDimensions = (dimensions: CanvasState['dimensions']): boolean => {
    return (
        dimensions.sourceType === DimensionSourceType.Default &&
        dimensions.useWidth === initialDefaultViewDimensions.useWidth &&
        dimensions.useHeight === initialDefaultViewDimensions.useHeight
    )
}

/**
 * 获取尺寸显示文本
 * @param dimensions 尺寸信息
 * @returns 显示文本
 */
export const getDimensionsDisplayText = (dimensions: CanvasState['dimensions']): string => {
    const { useWidth, useHeight, sourceType } = dimensions
    const source = sourceType === DimensionSourceType.Default ? '默认' : '自定义'
    return `${useWidth} × ${useHeight} (${source})`
}
