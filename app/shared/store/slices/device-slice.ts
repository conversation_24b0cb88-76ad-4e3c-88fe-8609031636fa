/**
 * @fileoverview 设备状态分片
 * @description 管理设备选择和相关状态
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, DeviceState } from '../types'

// ==================== 设备状态分片接口 ====================

export interface DeviceSlice {
    devices: DeviceState

    // 设备操作方法
    setDeviceCount: (count: number) => void
    setSelectedDeviceIndex: (index: number | null) => void
    resetDevices: () => void
}

// ==================== 默认状态 ====================

const defaultDeviceState: DeviceState = {
    selectedDeviceIndex: null,
}

// ==================== 设备状态分片创建函数 ====================

export const createDeviceSlice: StateCreator<UnifiedAppState, [], [], DeviceSlice> = (
    set,
    get,
) => ({
    // 初始状态
    devices: defaultDeviceState,

    // ==================== 设备操作方法 ====================

    /**
     * 设置设备数量
     * @param count 设备数量
     */
    setDeviceCount: (count: number) => {
        set(state => ({
            devices: {
                ...state.devices,
                // 这里可以添加设备数量相关的逻辑
            },
        }))

        console.log(`📱 设置设备数量: ${count}`)
    },

    /**
     * 设置选中的设备索引
     * @param index 设备索引，null表示取消选择
     */
    setSelectedDeviceIndex: (index: number | null) => {
        set(state => ({
            devices: {
                ...state.devices,
                selectedDeviceIndex: index,
            },
        }))

        console.log(`📱 设置选中设备索引: ${index}`)
    },

    /**
     * 重置设备状态
     */
    resetDevices: () => {
        set(state => ({
            devices: defaultDeviceState,
        }))

        console.log('🔄 已重置设备状态')
    },
})
