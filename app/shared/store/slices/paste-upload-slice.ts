/**
 * @fileoverview 粘贴上传状态分片
 * @description 管理粘贴上传功能相关状态
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState, PasteUploadState } from '../types'

// ==================== 粘贴上传状态分片接口 ====================

export interface PasteUploadSlice {
    pasteUpload: PasteUploadState
    
    // 粘贴上传操作方法
    setEnabled: (enabled: boolean) => void
    setPastedImage: (image: PasteUploadState['pastedImage']) => void
    setUploadStatus: (status: PasteUploadState['uploadStatus']) => void
    clearPastedImage: () => void
    resetPasteUpload: () => void
}

// ==================== 默认状态 ====================

const defaultPasteUploadState: PasteUploadState = {
    enabled: true,
    pastedImage: null,
    uploadStatus: 'idle',
}

// ==================== 粘贴上传状态分片创建函数 ====================

export const createPasteUploadSlice: StateCreator<
    UnifiedAppState,
    [],
    [],
    PasteUploadSlice
> = (set, get) => ({
    // 初始状态
    pasteUpload: defaultPasteUploadState,

    // ==================== 粘贴上传操作方法 ====================

    /**
     * 设置粘贴上传功能是否启用
     * @param enabled 是否启用
     */
    setEnabled: (enabled: boolean) => {
        set((state) => ({
            pasteUpload: {
                ...state.pasteUpload,
                enabled,
            },
        }))

        console.log(`📋 设置粘贴上传启用状态: ${enabled}`)
    },

    /**
     * 设置粘贴的图片数据
     * @param image 图片数据
     */
    setPastedImage: (image: PasteUploadState['pastedImage']) => {
        set((state) => ({
            pasteUpload: {
                ...state.pasteUpload,
                pastedImage: image,
            },
        }))

        console.log(`📋 设置粘贴图片:`, image?.file.name)
    },

    /**
     * 设置上传状态
     * @param status 上传状态
     */
    setUploadStatus: (status: PasteUploadState['uploadStatus']) => {
        set((state) => ({
            pasteUpload: {
                ...state.pasteUpload,
                uploadStatus: status,
            },
        }))

        console.log(`📋 设置上传状态: ${status}`)
    },

    /**
     * 清除粘贴的图片数据
     */
    clearPastedImage: () => {
        set((state) => ({
            pasteUpload: {
                ...state.pasteUpload,
                pastedImage: null,
                uploadStatus: 'idle',
            },
        }))

        console.log('📋 已清除粘贴图片')
    },

    /**
     * 重置粘贴上传状态
     */
    resetPasteUpload: () => {
        set((state) => ({
            pasteUpload: defaultPasteUploadState,
        }))

        console.log('🔄 已重置粘贴上传状态')
    },
})
