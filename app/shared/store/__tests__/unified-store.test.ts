/**
 * @fileoverview 统一状态管理测试
 * @description 测试统一状态管理的基本功能
 */

import { describe, it, expect, beforeEach } from '@jest/globals'
import { useUnifiedStore } from '../index'

// Mock localStorage for testing
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
})

describe('统一状态管理测试', () => {
    beforeEach(() => {
        // 重置store状态
        useUnifiedStore.getState().resetImages()
        useUnifiedStore.getState().resetBackground()
        useUnifiedStore.getState().resetMockup()
        useUnifiedStore.getState().resetScene()
        
        // 清除localStorage mock
        localStorageMock.getItem.mockClear()
        localStorageMock.setItem.mockClear()
        localStorageMock.removeItem.mockClear()
    })

    describe('UI状态管理', () => {
        it('应该正确设置移动设备状态', () => {
            const store = useUnifiedStore.getState()
            
            store.setIsMobile(true)
            expect(useUnifiedStore.getState().ui.isMobile).toBe(true)
            
            store.setIsMobile(false)
            expect(useUnifiedStore.getState().ui.isMobile).toBe(false)
        })

        it('应该正确切换侧边栏状态', () => {
            const store = useUnifiedStore.getState()
            
            store.setSidebarOpen(true)
            expect(useUnifiedStore.getState().ui.sidebarOpen).toBe(true)
            
            store.setSidebarOpen(false)
            expect(useUnifiedStore.getState().ui.sidebarOpen).toBe(false)
        })
    })

    describe('模态框状态管理', () => {
        it('应该正确设置模态框状态', () => {
            const store = useUnifiedStore.getState()
            
            store.setModalState('mediaPickerModal', true)
            expect(useUnifiedStore.getState().modals.mediaPickerModal).toBe(true)
            
            store.setModalState('mediaPickerModal', false)
            expect(useUnifiedStore.getState().modals.mediaPickerModal).toBe(false)
        })

        it('应该正确切换模态框状态', () => {
            const store = useUnifiedStore.getState()
            
            // 初始状态应该是false
            expect(useUnifiedStore.getState().modals.exportPopover).toBe(false)
            
            store.toggleModal('exportPopover')
            expect(useUnifiedStore.getState().modals.exportPopover).toBe(true)
            
            store.toggleModal('exportPopover')
            expect(useUnifiedStore.getState().modals.exportPopover).toBe(false)
        })
    })

    describe('图片状态管理', () => {
        it('应该正确更新拖拽状态', () => {
            const store = useUnifiedStore.getState()
            
            store.updateDragState({ isDragging: true, dragOverTarget: 'device-1' })
            
            const dragState = useUnifiedStore.getState().images.dragState
            expect(dragState.isDragging).toBe(true)
            expect(dragState.dragOverTarget).toBe('device-1')
        })

        it('应该正确设置选中图片', () => {
            const store = useUnifiedStore.getState()
            
            store.selectImage('test-image-id')
            expect(useUnifiedStore.getState().images.selectedImageId).toBe('test-image-id')
            
            store.selectImage(null)
            expect(useUnifiedStore.getState().images.selectedImageId).toBe(null)
        })

        it('应该正确设置当前选中设备', () => {
            const store = useUnifiedStore.getState()
            
            store.setCurrentSelectedDevice(1)
            expect(useUnifiedStore.getState().images.currentSelectedDevice).toBe(1)
            
            store.setCurrentSelectedDevice(null)
            expect(useUnifiedStore.getState().images.currentSelectedDevice).toBe(null)
        })
    })

    describe('背景状态管理', () => {
        it('应该正确设置背景类型', () => {
            const store = useUnifiedStore.getState()
            
            store.setBackgroundType('COLOR' as any)
            expect(useUnifiedStore.getState().background.backgroundType).toBe('COLOR')
        })

        it('应该正确设置背景颜色', () => {
            const store = useUnifiedStore.getState()
            
            store.setBackgroundColor('#ff0000')
            expect(useUnifiedStore.getState().background.color).toBe('#ff0000')
        })
    })

    describe('模型状态管理', () => {
        it('应该正确设置激活样式', () => {
            const store = useUnifiedStore.getState()
            
            store.setActiveStyle('Black' as any)
            expect(useUnifiedStore.getState().mockup.activeStyle).toBe('Black')
        })
    })

    describe('状态重置功能', () => {
        it('应该正确重置图片状态', () => {
            const store = useUnifiedStore.getState()
            
            // 设置一些状态
            store.selectImage('test-id')
            store.updateDragState({ isDragging: true })
            
            // 重置状态
            store.resetImages()
            
            const imageState = useUnifiedStore.getState().images
            expect(imageState.selectedImageId).toBe(null)
            expect(imageState.dragState.isDragging).toBe(false)
        })
    })
})
