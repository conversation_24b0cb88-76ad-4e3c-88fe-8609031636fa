# 统一状态管理迁移指南

## 🎯 概述

本项目已将分散的状态管理（useImageStore、useBackgroundStore、useMockupStore、useSceneStore等）整合为统一的状态管理系统。

## 📦 新的状态管理结构

### 统一Store导入
```typescript
// 旧方式
import { useImageStore } from './ImageMange/imageMangeIndex'
import { useBackgroundStore } from './hooks/useBackgroundStore'
import { useMockupStore } from './hooks/useMockupStore'
import { useSceneStore } from './hooks/useSceneStore'

// 新方式
import { useUnifiedStore } from './shared/store'
```

### 状态访问方式

#### 图片状态
```typescript
// 旧方式
const { addImages, setImageDevice, dragState } = useImageStore()

// 新方式
const { addImages, setImageDevice, updateDragState, getImageForDevice } = useUnifiedStore()
const dragState = useUnifiedStore(state => state.images.dragState)
```

#### 背景状态
```typescript
// 旧方式
const { backgroundType, color, setBackgroundType } = useBackgroundStore()

// 新方式
const { backgroundType, color, setBackgroundType } = useUnifiedStore(state => state.background)
```

#### 模型状态
```typescript
// 旧方式
const { styles, activeStyle, setActiveStyle } = useMockupStore()

// 新方式
const { styles, activeStyle, setActiveStyle } = useUnifiedStore(state => state.mockup)
```

#### 场景状态
```typescript
// 旧方式
const { sceneType, shadowType, setShadowType } = useSceneStore()

// 新方式
const { sceneType, shadowType, setShadowType } = useUnifiedStore(state => state.scene)
```

## 🔄 方法名变更

### 拖拽状态更新
```typescript
// 旧方式
setDragState({ isDragging: true })

// 新方式
updateDragState({ isDragging: true })
```

### 状态获取
```typescript
// 旧方式
const images = useImageStore.getState().images

// 新方式
const images = useUnifiedStore.getState().images.images
```

## 📁 文件结构

```
app/shared/store/
├── index.ts                    # 统一Store导出
├── types.ts                    # 类型定义
├── slices/                     # 状态分片
│   ├── ui-slice.ts
│   ├── modal-slice.ts
│   ├── canvas-slice.ts
│   ├── image-slice.ts
│   ├── device-slice.ts
│   ├── background-slice.ts
│   ├── scene-slice.ts
│   ├── mockup-slice.ts
│   ├── export-slice.ts
│   └── paste-upload-slice.ts
└── middleware/
    ├── sync-middleware.ts      # 状态同步中间件
    └── persist-middleware.ts   # 持久化中间件
```

## ✅ 已迁移的组件

- ✅ DisplayContainer.tsx
- ✅ page.tsx
- ✅ PcMockup_Media.tsx
- ✅ MobileFrame_Magic.tsx (部分)

## 🔧 待完成的工作

1. 完善useCustomImageStore与统一状态管理的集成
2. 迁移剩余的组件
3. 删除旧的状态管理文件
4. 更新类型定义
5. 添加单元测试

## 🚨 注意事项

1. 状态访问方式发生变化，需要使用选择器函数
2. 某些方法名已更改（如setDragState → updateDragState）
3. 状态结构层级发生变化（如images.images而不是images）
4. 持久化配置已集成，用户偏好会自动保存

## 🐛 常见问题

### Q: 为什么状态没有更新？
A: 检查是否使用了正确的选择器函数和新的方法名。

### Q: 如何访问嵌套状态？
A: 使用选择器函数：`useUnifiedStore(state => state.images.dragState)`

### Q: 旧的store文件何时删除？
A: 在所有组件迁移完成并测试通过后删除。
