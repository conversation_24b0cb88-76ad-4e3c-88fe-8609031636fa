/**
 * @fileoverview 统一状态管理类型定义
 * @description 定义所有状态分片的类型和统一应用状态结构
 */

// ==================== 导入外部类型 ====================

import { ImageItem, DragState } from '../../ImageMange/imageMangeIndex'
import { sizeInfo } from '../../features/viewDimensions/utils/重构/dimensions'
import {
    DimensionSourceType,
    ImageExportFormat,
    ExportQualitySetting,
} from '../../features/viewDimensions/utils/重构/状态管理'
import { BackgroundTypeEnum } from '../../hooks/useBackgroundStore'
import { MockupStyleEnum } from '../../hooks/useMockupStore'
import { SceneTabType, ShadowLayerType, ShadowType } from '../../hooks/useSceneStore'
import { ShadowTypeEnum } from '../../hooks/useMockupShadowStore'

// ==================== UI 状态类型 ====================

export interface UIState {
    /** 是否为移动设备 */
    isMobile: boolean
    /** 当前激活的标签页 */
    activeTab: string
    /** 侧边栏是否打开 */
    sidebarOpen: boolean
}

// ==================== 模态框状态类型 ====================

export interface ModalState {
    /** 媒体选择器模态框 */
    mediaPickerModal: boolean
    /** 导出弹窗 */
    exportPopover: boolean
    /** 成功提示弹窗 */
    successPopover: boolean
    /** 颜色选择器模态框 */
    colorPickerModal: boolean
    /** 图片选择器模态框 */
    imagePickerModal: boolean
}

// ==================== 画布状态类型 ====================

export interface CanvasState {
    /** 视图尺寸信息 */
    dimensions: sizeInfo & {
        sourceType: DimensionSourceType
        calculatedWidth?: number
        calculatedHeight?: number
        calculationMethod?: string
    }
    /** 视图模式 */
    viewMode: 'preview' | 'edit'
    /** 当前激活的布局 */
    activeLayout: {
        type: 'single' | 'dual' | 'triple'
        id: number
    }
    /** 缩放比例 */
    scale: number
}

// ==================== 图片状态类型 ====================

export interface ImageState {
    /** 图片列表 */
    images: ImageItem[]
    /** 当前选中的图片ID */
    selectedImageId: string | null
    /** 拖拽状态 */
    dragState: DragState
    /** 上传进度 */
    uploadProgress: Record<string, number>
    /** 当前选中的设备索引 */
    currentSelectedDevice: number | null
}

// ==================== 设备状态类型 ====================

export interface DeviceState {
    /** 当前选中的设备索引 */
    selectedDeviceIndex: number | null
}

// ==================== 背景状态类型 ====================

export interface BackgroundState {
    /** 背景类型 */
    backgroundType: BackgroundTypeEnum
    /** 背景颜色 */
    color: string
    /** Unsplash图片信息 */
    unsplashImage: {
        url: string
        author: string
    } | null
    /** 自定义图片背景 */
    customImage: {
        file: File
        preview: string
    } | null
    /** 颜色选择器是否可见 */
    isColorPickerVisible: boolean
    /** 图片选择器是否可见 */
    isImagePickerVisible: boolean
    /** 魔法背景配置 */
    magicBackground: {
        type: 'gradient' | 'mesh' | 'solid'
        value: {
            background: string
            imageUrl?: string
        }
    }
}

// ==================== 场景状态类型 ====================

export interface SceneState {
    /** 场景类型 */
    sceneType: SceneTabType
    /** 阴影类型 */
    shadowType: ShadowType
    /** 阴影透明度 */
    shadowOpacity: number
    /** 阴影层级 */
    shadowLayer: ShadowLayerType
    /** 模型阴影配置 */
    mockupShadow: {
        type: ShadowTypeEnum
        opacity: number
        position: { x: number; y: number }
    }
}

// ==================== 模型状态类型 ====================

export interface MockupState {
    /** 可用样式列表 */
    styles: Array<{
        label: MockupStyleEnum
        src: string
        deviceSrc: string
    }>
    /** 当前激活的样式 */
    activeStyle: MockupStyleEnum
}

// ==================== 导出状态类型 ====================

export interface ExportState {
    /** 导出设置 */
    settings: {
        format: ImageExportFormat
        quality: ExportQualitySetting
        scale: number
    }
    /** 导出进度 */
    progress: number
    /** 是否正在导出 */
    isExporting: boolean
    /** 导出状态 */
    status: 'idle' | 'exporting' | 'success' | 'error'
}

// ==================== 粘贴上传状态类型 ====================

export interface PasteUploadState {
    /** 是否启用粘贴上传 */
    enabled: boolean
    /** 粘贴的图片数据 */
    pastedImage: {
        file: File
        preview: string
    } | null
    /** 上传状态 */
    uploadStatus: 'idle' | 'uploading' | 'success' | 'error'
}

// ==================== 统一应用状态类型 ====================

export interface UnifiedAppState {
    // 状态数据
    ui: UIState
    modals: ModalState
    canvas: CanvasState
    images: ImageState
    devices: DeviceState
    background: BackgroundState
    scene: SceneState
    mockup: MockupState
    export: ExportState
    pasteUpload: PasteUploadState

    // UI 操作方法
    setIsMobile: (isMobile: boolean) => void
    setActiveTab: (tab: string) => void
    toggleSidebar: () => void
    setSidebarOpen: (open: boolean) => void
    resetUI: () => void

    // 模态框操作方法
    setModalState: (modalName: keyof ModalState, isOpen: boolean) => void
    toggleModal: (modalName: keyof ModalState) => void
    closeAllModals: () => void
    resetModals: () => void

    // 画布操作方法
    setDimensions: (dimensions: CanvasState['dimensions']) => void
    setViewMode: (mode: CanvasState['viewMode']) => void
    setScale: (scale: number) => void
    setActiveLayout: (layout: CanvasState['activeLayout']) => void
    resetCanvas: () => void

    // 图片操作方法
    addImages: (files: File[]) => Promise<void>
    removeImage: (id: string) => void
    removeImages: (ids: string[]) => void
    selectImage: (id: string | null) => void
    updateDragState: (dragState: Partial<DragState>) => void
    updateImageStatus: (id: string, status: any) => void
    setImageDevice: (imageId: string, deviceIndex: number) => void
    unbindImageFromDevice: (imageId: string, deviceIndex: number) => void
    removeImageFromDevice: (imageId: string, deviceIndex: number) => void
    getImageForDevice: (deviceIndex: number) => ImageItem | null
    setCurrentSelectedDevice: (deviceIndex: number | null) => void
    selectImageForCurrentDevice: (imageId: string) => void
    getImagesByDevice: (deviceIndex?: number) => ImageItem[]
    getNextAvailableDevice: () => number
    isImageBindToDevice: (imageId: string, deviceIndex: number) => boolean
    updateUploadProgress: (imageId: string, progress: number) => void
    clearAllImages: () => void
    resetImages: () => void

    // 设备操作方法
    setDeviceCount: (count: number) => void
    resetDevices: () => void

    // 背景操作方法
    setBackgroundType: (type: BackgroundTypeEnum) => void
    setBackgroundColor: (color: string) => void
    setColor: (newColor: string) => void
    setUnsplashImage: (image: { url: string; author: string } | null) => void
    setCustomImage: (image: { file: File; preview: string } | null) => void
    setColorPickerVisible: (visible: boolean) => void
    setImagePickerVisible: (visible: boolean) => void
    toggleColorPicker: (visible?: boolean) => void
    toggleImagePicker: (visible?: boolean) => void
    setTransparent: () => void
    setImageBackground: () => void
    setMagicBackgroundType: () => void
    setMagicBackground: (config: BackgroundState['magicBackground']) => void
    resetBackground: () => void

    // 场景操作方法
    setSceneType: (type: SceneTabType) => void
    setShadowType: (type: ShadowType) => void
    setShadowOpacity: (opacity: number) => void
    setShadowLayer: (layer: ShadowLayerType) => void
    setMockupShadow: (config: SceneState['mockupShadow']) => void
    setMockupShadowType: (type: ShadowTypeEnum) => void
    setMockupShadowOpacity: (opacity: number) => void
    setMockupShadowPosition: (position: { x: number; y: number }) => void
    resetScene: () => void

    // 模型操作方法
    setActiveStyle: (style: MockupStyleEnum) => void
    setStyles: (styles: MockupState['styles']) => void
    resetMockup: () => void

    // 导出操作方法
    setExportFormat: (format: ImageExportFormat) => void
    setExportQuality: (quality: ExportQualitySetting) => void
    setExportProgress: (progress: number) => void
    setExportStatus: (status: ExportState['status']) => void
    startExport: () => void
    completeExport: () => void
    resetExport: () => void

    // 粘贴上传操作方法
    setEnabled: (enabled: boolean) => void
    setUploadStatus: (status: PasteUploadState['uploadStatus']) => void
    resetPasteUpload: () => void
}
