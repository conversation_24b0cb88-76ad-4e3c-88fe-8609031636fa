/**
 * @fileoverview 持久化中间件配置
 * @description 配置状态持久化存储，选择性保存状态到localStorage
 */

import { PersistOptions } from 'zustand/middleware'
import { UnifiedAppState } from '../types'

// ==================== 持久化配置 ====================

/**
 * 创建持久化配置
 * @description 配置哪些状态需要持久化，哪些需要排除
 */
export const createPersistConfig = (): PersistOptions<UnifiedAppState> => ({
    name: 'unified-app-storage',
    version: 1,

    // 选择性持久化：只保存需要的状态
    partialize: state => ({
        // UI状态 - 保存用户偏好
        ui: {
            activeTab: state.ui.activeTab,
            sidebarOpen: state.ui.sidebarOpen,
            // 不保存 isMobile，每次重新检测
        },

        // 画布状态 - 保存视图设置
        canvas: {
            dimensions: state.canvas.dimensions,
            viewMode: state.canvas.viewMode,
            scale: state.canvas.scale,
            // 不保存 activeLayout，每次重置
        },

        // 导出状态 - 保存用户偏好
        export: {
            settings: state.export.settings,
            // 不保存进度和导出状态
        },

        // 背景状态 - 保存背景设置
        background: {
            backgroundType: state.background.backgroundType,
            color: state.background.color,
            magicBackground: state.background.magicBackground,
            // 不保存图片和选择器状态
        },

        // 场景状态 - 保存场景设置
        scene: {
            sceneType: state.scene.sceneType,
            shadowType: state.scene.shadowType,
            shadowOpacity: state.scene.shadowOpacity,
            shadowLayer: state.scene.shadowLayer,
        },

        // 模型状态 - 保存激活样式
        mockup: {
            activeStyle: state.mockup.activeStyle,
            // styles 数组不需要持久化，每次重新加载
        },

        // 粘贴上传状态 - 保存启用状态
        pasteUpload: {
            enabled: state.pasteUpload.enabled,
            // 不保存临时数据
        },

        // 不持久化的状态：
        // - modals: 模态框状态应该每次重置
        // - images: 图片数据不持久化，避免内存问题
        // - devices: 设备状态每次重置
    }),

    // 状态合并配置 - 确保持久化状态与默认状态正确合并
    merge: (persistedState: any, currentState: any) => {
        console.log('🔄 合并持久化状态与默认状态')

        // 深度合并，确保未持久化的属性使用默认值
        const mergedState = {
            ...currentState,
            ...persistedState,
            // 特殊处理 mockup 状态，确保 styles 数组存在
            mockup: {
                ...currentState.mockup,
                ...persistedState.mockup,
                // 确保 styles 数组始终使用默认值
                styles: currentState.mockup?.styles || [],
            },
            // 特殊处理 scene 状态，确保 mockupShadow 对象存在
            scene: {
                ...currentState.scene,
                ...persistedState.scene,
                // 确保 mockupShadow 对象始终存在且结构正确
                mockupShadow: {
                    // 首先使用默认值
                    ...currentState.scene?.mockupShadow,
                    // 然后合并持久化的值（如果存在且结构正确）
                    ...(persistedState.scene?.mockupShadow &&
                    typeof persistedState.scene.mockupShadow === 'object' &&
                    persistedState.scene.mockupShadow.type !== undefined
                        ? persistedState.scene.mockupShadow
                        : {}),
                },
            },
        }

        console.warn('📊 合并后的 mockup 状态:', mergedState.mockup)
        console.warn('📊 合并后的 scene 状态:', mergedState.scene)
        console.warn('📊 合并后的 mockupShadow 状态:', mergedState.scene?.mockupShadow)
        return mergedState
    },

    // 状态迁移配置
    migrate: (persistedState: any, version: number) => {
        console.log(`🔄 状态迁移: v${version} → v1`)

        // 版本0到版本1的迁移
        if (version === 0) {
            return {
                ...persistedState,
                // 添加新的默认值或转换旧数据
            }
        }

        return persistedState
    },

    // 存储配置
    storage: {
        getItem: (name: string) => {
            try {
                // 检查是否在浏览器环境中
                if (typeof window === 'undefined' || !window.localStorage) {
                    return null
                }
                const item = localStorage.getItem(name)
                return item ? JSON.parse(item) : null
            } catch (error) {
                console.warn('📦 读取持久化状态失败:', error)
                return null
            }
        },
        setItem: (name: string, value: any) => {
            try {
                // 检查是否在浏览器环境中
                if (typeof window === 'undefined' || !window.localStorage) {
                    return
                }
                localStorage.setItem(name, JSON.stringify(value))
            } catch (error) {
                console.warn('📦 保存持久化状态失败:', error)
            }
        },
        removeItem: (name: string) => {
            try {
                // 检查是否在浏览器环境中
                if (typeof window === 'undefined' || !window.localStorage) {
                    return
                }
                localStorage.removeItem(name)
            } catch (error) {
                console.warn('📦 删除持久化状态失败:', error)
            }
        },
    },

    // 跳过水合检查，避免SSR问题
    skipHydration: false,
})

// ==================== 工具函数 ====================

/**
 * 清除持久化存储
 */
export const clearPersistedState = () => {
    try {
        localStorage.removeItem('unified-app-storage')
        console.log('🧹 已清除持久化状态')
    } catch (error) {
        console.warn('🧹 清除持久化状态失败:', error)
    }
}

/**
 * 获取持久化存储大小
 */
export const getStorageSize = (): string => {
    try {
        const data = localStorage.getItem('unified-app-storage')
        if (!data) return '0 B'

        const bytes = new Blob([data]).size
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(1024))

        return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
    } catch (error) {
        console.warn('📊 获取存储大小失败:', error)
        return '未知'
    }
}
