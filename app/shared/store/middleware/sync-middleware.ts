/**
 * @fileoverview 状态同步中间件
 * @description 处理状态之间的同步和依赖关系
 */

import { StateCreator } from 'zustand'
import { UnifiedAppState } from '../types'

// ==================== 同步中间件类型 ====================

export type SyncMiddleware = <T extends UnifiedAppState>(
    stateCreator: StateCreator<T, [], [], T>
) => StateCreator<T, [], [], T>

// ==================== 同步中间件实现 ====================

/**
 * 创建状态同步中间件
 * @description 监听状态变化并执行相应的同步逻辑
 */
export const createSyncMiddleware: SyncMiddleware = (stateCreator) => (set, get, api) => {
    const state = stateCreator(
        (partial, replace) => {
            // 在状态更新前执行同步逻辑
            if (typeof partial === 'function') {
                const newState = partial(get())
                handleStateSync(get(), newState)
                set(newState, replace)
            } else {
                handleStateSync(get(), partial)
                set(partial, replace)
            }
        },
        get,
        api
    )

    return state
}

// ==================== 同步逻辑处理 ====================

/**
 * 处理状态同步逻辑
 * @param currentState 当前状态
 * @param newState 新状态
 */
function handleStateSync(currentState: UnifiedAppState, newState: Partial<UnifiedAppState>) {
    // 图片选择同步到设备状态
    if (newState.images?.selectedImageId !== currentState.images?.selectedImageId) {
        console.log('🔄 同步图片选择状态')
    }

    // 设备选择同步到图片状态
    if (newState.devices?.selectedDeviceIndex !== currentState.devices?.selectedDeviceIndex) {
        console.log('🔄 同步设备选择状态')
    }

    // 模态框状态同步
    if (newState.modals && hasModalStateChanged(currentState.modals, newState.modals)) {
        console.log('🔄 同步模态框状态')
    }

    // 背景状态同步到场景
    if (newState.background?.backgroundType !== currentState.background?.backgroundType) {
        console.log('🔄 同步背景状态到场景')
    }
}

/**
 * 检查模态框状态是否发生变化
 */
function hasModalStateChanged(
    currentModals: UnifiedAppState['modals'],
    newModals: Partial<UnifiedAppState['modals']>
): boolean {
    return Object.keys(newModals).some(key => {
        const modalKey = key as keyof UnifiedAppState['modals']
        return newModals[modalKey] !== currentModals[modalKey]
    })
}
