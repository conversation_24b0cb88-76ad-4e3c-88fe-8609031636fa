/**
 * @fileoverview 统一状态管理系统
 * @description 整合所有应用状态到单一store中，提供统一的状态管理接口
 */

import { create } from 'zustand'
import { subscribeWithSelector, devtools, persist } from 'zustand/middleware'
import { UnifiedAppState } from './types'
import { createUISlice } from './slices/ui-slice'
import { createModalSlice } from './slices/modal-slice'
import { createCanvasSlice } from './slices/canvas-slice'
import { createImageSlice } from './slices/image-slice'
import { createDeviceSlice } from './slices/device-slice'
import { createBackgroundSlice } from './slices/background-slice'
import { createSceneSlice } from './slices/scene-slice'
import { createMockupSlice } from './slices/mockup-slice'
import { createExportSlice } from './slices/export-slice'
import { createPasteUploadSlice } from './slices/paste-upload-slice'
import { createPersistConfig } from './middleware/persist-middleware'
import { createSyncMiddleware } from './middleware/sync-middleware'

// ==================== 统一状态管理Store ====================

/**
 * 统一状态管理Store
 * @description 整合所有应用状态的单一store
 */
export const useUnifiedStore = create<UnifiedAppState>()(
    // 开发工具中间件
    devtools(
        // 持久化中间件
        persist(
            // 状态选择器中间件
            subscribeWithSelector(
                // 状态同步中间件
                createSyncMiddleware(
                    // 状态分片组合
                    (set, get, api) => ({
                        // 合并所有状态分片
                        ...createUISlice(set, get, api),
                        ...createModalSlice(set, get, api),
                        ...createCanvasSlice(set, get, api),
                        ...createImageSlice(set, get, api),
                        ...createDeviceSlice(set, get, api),
                        ...createBackgroundSlice(set, get, api),
                        ...createSceneSlice(set, get, api),
                        ...createMockupSlice(set, get, api),
                        ...createExportSlice(set, get, api),
                        ...createPasteUploadSlice(set, get, api),
                    })
                )
            ),
            // 持久化配置
            createPersistConfig()
        ),
        // 开发工具配置
        {
            name: 'unified-app-store',
            enabled: process.env.NODE_ENV === 'development',
        }
    )
)

// ==================== 类型导出 ====================

export type { UnifiedAppState } from './types'
export * from './types'

// ==================== 工具函数导出 ====================

export { clearPersistedState, getStorageSize } from './middleware/persist-middleware'

// ==================== 开发工具 ====================

/**
 * 获取当前完整状态（仅开发环境）
 */
export const getFullState = () => {
    if (process.env.NODE_ENV === 'development') {
        return useUnifiedStore.getState()
    }
    return null
}

/**
 * 重置所有状态到默认值（仅开发环境）
 */
export const resetAllStates = () => {
    if (process.env.NODE_ENV === 'development') {
        const store = useUnifiedStore.getState()
        store.resetUI()
        store.resetModals()
        store.resetCanvas()
        store.resetImages()
        store.resetDevices()
        store.resetBackground()
        store.resetScene()
        store.resetMockup()
        store.resetExport()
        store.resetPasteUpload()
        console.log('🔄 所有状态已重置到默认值')
    }
}

// ==================== 状态订阅工具 ====================

/**
 * 订阅特定状态变化
 * @param selector 状态选择器
 * @param callback 变化回调
 * @returns 取消订阅函数
 */
export const subscribeToState = <T>(
    selector: (state: UnifiedAppState) => T,
    callback: (value: T, previousValue: T) => void
) => {
    return useUnifiedStore.subscribe(selector, callback)
}

// ==================== 初始化 ====================

/**
 * 初始化统一状态管理系统
 * @description 在应用启动时调用，进行必要的初始化工作
 */
export const initializeStore = () => {
    console.log('🚀 统一状态管理系统初始化完成')
    
    // 在开发环境下输出状态信息
    if (process.env.NODE_ENV === 'development') {
        console.log('📊 当前状态结构:', Object.keys(useUnifiedStore.getState()))
    }
}
