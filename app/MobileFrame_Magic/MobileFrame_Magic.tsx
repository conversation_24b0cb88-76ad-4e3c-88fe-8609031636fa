import { useIsMobile } from '../hooks/useAppState'
import { useColorData, useMultipleColorData } from './hooks/useColorData'
import { useMagicBackgroundStore, MagicBackgroundType } from '../hooks/useMagicBackgroundStore'
import { useUnifiedStore } from '../shared/store'
import { BackgroundTypeEnum } from '../hooks/useBackgroundStore'
import { useMagicBackground } from '../hooks/useMagicBackground'
import React, { useState, ReactNode, useEffect } from 'react'
import * as colorData1 from './jsx结构与生成颜色结构体/colorData1'
import * as colorData2 from './jsx结构与生成颜色结构体/colorData2'
import * as colorData5 from './jsx结构与生成颜色结构体/colorData5'

/**
 * @description 颜色数据源枚举
 */
enum ColorDataSource {
    DATA1 = 'colorData1',
    DATA2 = 'colorData2',
    DATA5 = 'colorData5',
    DATA8 = 'colorData8',
    DATA9 = 'colorData9',
    DATA10 = 'colorData10',
}

/**
 * @description 颜色数据接口
 */
interface IColorData {
    solidColors: any[]
    gradientColors: any[]
    meshColors: any[]
    imageBackgrounds: any[]
    DEFAULT_WALLPAPER_PATH: string
}

/**
 * @description MobileFrame_Magic 主组件
 * @returns {React.ReactElement} 渲染的组件
 */
export const MobileFrame_Magic = (): React.ReactElement => {
    const isMobile = useIsMobile()

    // 获取背景状态管理方法 - 必须在顶部调用，不能有条件逻辑
    const { setMagicBackground } = useMagicBackground()

    const tabsLIs = [
        MagicBackgroundType.GRADIENT,
        MagicBackgroundType.MESH,
        MagicBackgroundType.SOLID,
    ]

    const [currentSelectTab, setCurrentSelectTab] = useState<MagicBackgroundType>(
        MagicBackgroundType.SOLID,
    )

    /**
     * @description 当前颜色数据源状态
     */
    const [currentDataSource, setCurrentDataSource] = useState<ColorDataSource>(
        ColorDataSource.DATA5,
    )

    /**
     * @description 当前颜色数据状态
     */
    const [colorData, setColorData] = useState<IColorData | null>(null)

    /**
     * @description 壁纸图片路径状态，默认值从data.ts导入
     */
    const [wallpaperPath, setWallpaperPath] = useState<string>('/__壁纸测试/walller__5.jpg')

    // 动态颜色数据配置 - 使用数组方式统一管理
    const dynamicImagePaths = [
        '/__壁纸测试/walller__1.jpg', // DATA8
        '/__壁纸测试/walller__2.jpg', // DATA9
        '/__壁纸测试/walller__5.jpg', // DATA10
    ]

    // 使用批量Hook生成所有动态颜色数据
    const [colorData8, colorData9, colorData10] = useMultipleColorData(dynamicImagePaths)

    /**
     * @description 获取颜色数据模块
     * @param {ColorDataSource} source - 颜色数据源
     * @returns {any} 颜色数据模块
     */
    const getColorDataModule = (source: ColorDataSource): any => {
        if (source === ColorDataSource.DATA1) {
            return colorData1
        } else if (source === ColorDataSource.DATA2) {
            return colorData2
        } else if (source === ColorDataSource.DATA5) {
            return colorData5
        } else if (source === ColorDataSource.DATA8) {
            console.log('colorData8', colorData8)
            return colorData8
        } else if (source === ColorDataSource.DATA9) {
            console.log('colorData9', colorData9)
            return colorData9
        } else if (source === ColorDataSource.DATA10) {
            console.log('colorData10', colorData10)
            return colorData10
        }
    }

    /**
     * @description 加载颜色数据
     * @param {ColorDataSource} source - 颜色数据源
     * @returns {void}
     */
    const loadColorData = (source: ColorDataSource): void => {
        try {
            const colorModule = getColorDataModule(source)

            setColorData({
                solidColors: colorModule.solidColors || [],
                gradientColors: colorModule.gradientColors || [],
                meshColors: colorModule.meshColors || [],
                imageBackgrounds: colorModule.imageBackgrounds || [],
                DEFAULT_WALLPAPER_PATH: colorModule.DEFAULT_WALLPAPER_PATH,
            })

            setWallpaperPath(colorModule.DEFAULT_WALLPAPER_PATH)
        } catch (error) {
            console.error('加载颜色数据失败:', error)
        }
    }

    /**
     * @description 键盘事件监听器
     * @param {KeyboardEvent} event - 键盘事件
     */
    const handleKeyPress = (event: KeyboardEvent): void => {
        if (event.key === '4') {
            setCurrentDataSource(ColorDataSource.DATA1)
        } else if (event.key === '5') {
            setCurrentDataSource(ColorDataSource.DATA2)
        } else if (event.key === '6') {
            setCurrentDataSource(ColorDataSource.DATA5)
        } else if (event.key === '8') {
            setCurrentDataSource(ColorDataSource.DATA8)
        } else if (event.key === '9') {
            setCurrentDataSource(ColorDataSource.DATA9)
        } else if (event.key === '0') {
            setCurrentDataSource(ColorDataSource.DATA10)
        }
    }

    /**
     * @description 组件挂载时的副作用
     */
    useEffect(() => {
        // 添加键盘事件监听
        window.addEventListener('keydown', handleKeyPress)

        // 初始化加载默认颜色数据
        loadColorData(currentDataSource)

        // 清理函数：组件卸载时移除监听器
        return () => {
            window.removeEventListener('keydown', handleKeyPress)
        }
    }, [])

    /**
     * @description 监听数据源变化并重新加载数据
     */
    useEffect(() => {
        loadColorData(currentDataSource)
    }, [currentDataSource])

    // 如果颜色数据还未加载完成，显示加载状态
    if (!colorData) {
        return <div>正在加载颜色数据...</div>
    }

    const { solidColors, gradientColors, meshColors, imageBackgrounds } = colorData

    /**
     * @description 渲染Plus徽章组件
     * @param {number} width - 徽章宽度
     * @param {number} height - 徽章高度
     * @param {number} padding - 徽章内边距
     * @returns {React.ReactElement} 渲染的Plus徽章组件
     */
    const renderPlusBadge = (
        width: number,
        height: number,
        padding: number,
    ): React.ReactElement => {
        // return (
        //     <div className='plus-badge-wrapper' style={{ padding }}>
        //         <div className='plus-badge' style={{ width, height }}>
        //             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
        //                 <path
        //                     fill='currentColor'
        //                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
        //                 />
        //             </svg>
        //         </div>
        //     </div>
        // )
        return <></>
    }

    /**
     * @description 媒体控制组件
     * @returns {React.ReactElement} 媒体控制组件
     */
    const PublicMagicMediaControl = (): React.ReactElement => {
        /**
         * @description 颜色数据源配置数组
         */
        const colorDataSources: Array<{
            key: ColorDataSource
            module: IColorData
            label: string
        }> = [
            {
                key: ColorDataSource.DATA1,
                module: colorData1 as IColorData,
                label: 'Data1',
            },
            {
                key: ColorDataSource.DATA2,
                module: colorData2 as IColorData,
                label: 'Data2',
            },
            {
                key: ColorDataSource.DATA5,
                module: colorData5 as IColorData,
                label: 'Data5',
            },
            {
                key: ColorDataSource.DATA8,
                module: colorData8 as IColorData,
                label: 'Data8',
            },
            {
                key: ColorDataSource.DATA9,
                module: colorData9 as IColorData,
                label: 'Data9',
            },
            {
                key: ColorDataSource.DATA10,
                module: colorData10 as IColorData,
                label: 'Data10',
            },
        ]

        /**
         * @description 处理媒体项点击事件
         * @param {ColorDataSource} dataSource - 要切换到的颜色数据源
         */
        const handleMediaItemClick = (dataSource: ColorDataSource): void => {
            setCurrentDataSource(dataSource)
        }

        return (
            <div className='magic-media-control'>
                <div className='magic-media-picker'>
                    {colorDataSources.map(source => (
                        <div
                            key={source.key}
                            className='magic-media-item'
                            onClick={() => handleMediaItemClick(source.key)}
                            style={{ cursor: 'pointer' }}
                        >
                            <div
                                className={`colors-wrapper ${currentDataSource === source.key ? 'active' : ''}`}
                            >
                                {source.module.solidColors.slice(0, 5).map((color, colorIndex) => (
                                    <div
                                        key={colorIndex}
                                        className='pallet-color'
                                        style={{ background: color.background }}
                                    />
                                ))}
                                {/* 如果颜色数量超过5个，显示更多指示器 */}
                                {source.module.solidColors.length > 5 && (
                                    <div
                                        className='pallet-color'
                                        style={{
                                            background:
                                                source.module.solidColors[5]?.background ||
                                                source.module.solidColors[0].background,
                                            opacity: 0.7,
                                        }}
                                    />
                                )}
                            </div>
                            <div className='src-image'>
                                <img
                                    crossOrigin='anonymous'
                                    loading='lazy'
                                    decoding='async'
                                    alt={`preview-${source.label}`}
                                    src={source.module.DEFAULT_WALLPAPER_PATH}
                                />
                            </div>
                        </div>
                    ))}
                </div>
                <button
                    type='button'
                    className='button icon-button small-button undefined-button undefined-blur undefined-round true-active gray-text2'
                    style={{ flexDirection: 'row' }}
                >
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            d='M18.56 14.01c1.9 0 3.43 1.51 3.43 3.37s-1.54 3.37-3.44 3.37-3.44-1.52-3.44-3.38c0-1.87 1.54-3.38 3.43-3.38Zm-8.48 1.93c.83 0 1.5.66 1.5 1.48 0 .81-.68 1.48-1.51 1.48H3.49c-.84 0-1.51-.67-1.51-1.49s.67-1.49 1.5-1.49h6.57ZM5.43 2.98c1.89 0 3.43 1.51 3.43 3.37S7.32 9.72 5.42 9.72 1.98 8.2 1.98 6.34c0-1.87 1.53-3.38 3.43-3.38Zm15.05 1.89c.83 0 1.5.66 1.5 1.48 0 .81-.68 1.48-1.51 1.48h-6.58c-.84 0-1.51-.67-1.51-1.49s.67-1.49 1.5-1.49h6.57Z'
                        />
                    </svg>
                </button>
            </div>
        )
    }

    /**
     * @description 调色板自定义组件
     * @returns {React.ReactElement} 调色板自定义组件
     */
    const PublicMagicPalletCustomize = (): React.ReactElement => {
        return (
            <div className='magic-pallet-customize'>
                {renderPlusBadge(16, 16, 4)}
                <div className='pallet-colors'>
                    {solidColors.map((color, index) => (
                        <div key={index} className='trans-200 pallet-color-item '>
                            <div className='color-layer' style={{ background: color.background }} />
                            <div className='icon-overlay'>
                                <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    fill='currentColor'
                                    viewBox='0 0 24 24'
                                >
                                    <path d='M4.362 17.793c-.48.48-.49 1.332.01 1.831.51.5 1.361.49 1.832.02L12 13.846l5.788 5.788c.49.49 1.332.49 1.831-.01.5-.51.5-1.341.01-1.831l-5.788-5.788 5.788-5.798c.49-.49.5-1.332-.01-1.831-.499-.5-1.341-.5-1.83-.01L12 10.154 6.204 4.366c-.47-.48-1.332-.5-1.832.01-.5.5-.49 1.361-.01 1.831l5.788 5.798z' />
                                </svg>
                            </div>
                        </div>
                    ))}
                </div>
                <span>Click colors above to customize pallet</span>
            </div>
        )
    }

    /**
     * @description 展开按钮组件
     * @param {Object} props - 组件属性
     * @param {ReactNode} props.children - 子组件
     * @returns {React.ReactElement} 展开按钮组件
     */
    const PublicMagicExpandButton = ({ children }: { children: ReactNode }): React.ReactElement => {
        return (
            // is-expanded 点击之后增加的类名
            <div className='expand-button '>
                <div className='items-preview'>{children}</div>
                <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                    <path
                        fill='currentColor'
                        d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
                    />
                </svg>
            </div>
        )
    }

    /**
     * @description 处理纯色背景点击事件
     * @param {string} background - 背景颜色值
     */
    const handleSolidColorClick = (background: string) => {
        setMagicBackground(MagicBackgroundType.SOLID, { background })
    }

    /**
     * @description 处理渐变背景点击事件
     * @param {string} background - 渐变背景值
     */
    const handleGradientColorClick = (background: string) => {
        setMagicBackground(MagicBackgroundType.GRADIENT, { background })
    }

    /**
     * @description 处理网格背景点击事件
     * @param {string} backgroundColor - 背景颜色
     * @param {string} backgroundImage - 背景图片
     */
    const handleMeshColorClick = (backgroundColor: string, backgroundImage: string) => {
        setMagicBackground(MagicBackgroundType.MESH, { backgroundColor, backgroundImage })
    }

    /**
     * @description 处理图片背景点击事件
     * @param {string} background - 背景颜色
     * @param {string} imageUrl - 图片URL
     */
    const handleImageBackgroundClick = (background: string, imageUrl: string) => {
        setMagicBackground(MagicBackgroundType.IMAGE, { background, imageUrl })
    }

    /**
     * @description 渲染纯色背景列表
     * @returns {React.ReactElement} 渲染的纯色背景列表
     */
    const renderSolidColors = (): React.ReactElement => {
        return (
            <div className='backpack-new'>
                <div className='backpack-colors-list magic-list is-expanded'>
                    {solidColors.map((color, index) => (
                        <button
                            key={index}
                            className='background-item solid-item'
                            onClick={() => handleSolidColorClick(color.background)}
                        >
                            <div className='display false'>
                                <div style={{ background: color.background }} />
                            </div>
                        </button>
                    ))}
                    <PublicMagicExpandButton>
                        <button className='background-item solid-item'>
                            <div className='display false'>
                                <div
                                    style={{
                                        background: 'rgb(254, 254, 254)',
                                    }}
                                />
                            </div>
                        </button>
                        <button className='background-item solid-item'>
                            <div className='display false'>
                                <div
                                    style={{
                                        background: 'rgb(135, 101, 89)',
                                    }}
                                />
                            </div>
                        </button>
                    </PublicMagicExpandButton>
                </div>
            </div>
        )
    }

    /**
     * @description 渲染渐变背景列表
     * @returns {React.ReactElement} 渲染的渐变背景列表
     */
    const renderGradientColors = (): React.ReactElement => {
        return (
            <div className='backpack-new'>
                <div className='backpack-colors-list magic-list is-expanded'>
                    {gradientColors.map((color, index) => (
                        <button
                            key={index}
                            className='background-item gradient-item'
                            onClick={() => handleGradientColorClick(color.background)}
                        >
                            {color.showPlusBadge && renderPlusBadge(14, 14, 3)}
                            <div className='display false'>
                                <div style={{ background: color.background }} />
                            </div>
                        </button>
                    ))}
                    <PublicMagicExpandButton>
                        <button className='background-item gradient-item'>
                            <div className='display false'>
                                <div
                                    style={{
                                        background:
                                            'radial-gradient(circle at 50% 115%, rgb(181, 29, 24) 0%, rgb(255, 50, 42) 15%, rgb(255, 71, 59) 30%, rgb(255, 93, 77) 45%, rgb(255, 114, 94) 60%, rgb(255, 135, 112) 75%)',
                                    }}
                                />
                            </div>
                        </button>
                        <button className='background-item gradient-item'>
                            <div className='display false'>
                                <div
                                    style={{
                                        background:
                                            'radial-gradient(circle at 50% 115%, rgb(135, 101, 89) 0%, rgb(234, 175, 154) 15%, rgb(255, 248, 219) 30%)',
                                    }}
                                />
                            </div>
                        </button>
                    </PublicMagicExpandButton>
                </div>
            </div>
        )
    }

    /**
     * @description 渲染网格背景列表
     * @returns {React.ReactElement} 渲染的网格背景列表
     */
    const renderMeshColors = (): React.ReactElement => {
        return (
            <div className='backpack-new'>
                <div className='backpack-colors-list magic-list is-expanded'>
                    {meshColors.map((color, index) => (
                        <div
                            key={index}
                            className='background-item mesh-item'
                            style={{ fontSize: 1 }}
                            onClick={() =>
                                handleMeshColorClick(color.backgroundColor, color.backgroundImage)
                            }
                        >
                            {color.showPlusBadge && renderPlusBadge(14, 14, 3)}
                            <div className='display '>
                                <div className={`mesh-display ${color.className}`}>
                                    <div
                                        style={{
                                            backgroundColor: color.backgroundColor,
                                            backgroundImage: color.backgroundImage,
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    ))}
                    <PublicMagicExpandButton>
                        <div className='background-item mesh-item' style={{ fontSize: 1 }}>
                            <div className='display '>
                                <div className='mesh-display mesh-5-4'>
                                    <div
                                        style={{
                                            backgroundColor: 'rgb(135, 101, 89)',
                                            backgroundImage:
                                                'radial-gradient(at 41% 59%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 24% 34%, rgb(254, 254, 254) 0px, transparent 50%), radial-gradient(at 66% 76%, rgb(235, 189, 93) 0px, transparent 50%), radial-gradient(at 48% 53%, rgb(13, 3, 2) 0px, transparent 50%)',
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className='background-item mesh-item' style={{ fontSize: 1 }}>
                            <div className='display '>
                                <div className='mesh-display mesh-5-5'>
                                    <div
                                        style={{
                                            backgroundColor: 'rgb(135, 101, 89)',
                                            backgroundImage:
                                                'radial-gradient(at 69% 53%, rgb(181, 29, 24) 0px, transparent 50%), radial-gradient(at 5% 98%, rgb(254, 254, 254) 0px, transparent 50%), radial-gradient(at 38% 49%, rgb(235, 189, 93) 0px, transparent 50%), radial-gradient(at 30% 96%, rgb(13, 3, 2) 0px, transparent 50%)',
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </PublicMagicExpandButton>
                </div>
            </div>
        )
    }

    /**
     * @description 渲染图片背景列表
     * @returns {React.ReactElement} 渲染的图片背景列表
     */
    const renderImageBackgrounds = (): React.ReactElement => {
        return (
            <div className='backpack-new'>
                <div className='backpack-colors-list magic-list is-expanded'>
                    {imageBackgrounds.map((image, index) => (
                        <button
                            key={index}
                            className='background-item magic-image-item '
                            style={{ fontSize: 1 }}
                            onClick={() =>
                                handleImageBackgroundClick(image.background, wallpaperPath)
                            }
                        >
                            {image.showPlusBadge && renderPlusBadge(14, 14, 3)}
                            <div className='display false'>
                                <div
                                    className='magic-image-display style-2'
                                    style={{
                                        background: image.background,
                                    }}
                                >
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        alt='magicImage'
                                        src={wallpaperPath}
                                    />
                                </div>
                            </div>
                        </button>
                    ))}
                    <PublicMagicExpandButton>
                        <button
                            className='background-item magic-image-item '
                            style={{ fontSize: 1 }}
                        >
                            <div className='display false'>
                                <div
                                    className='magic-image-display style-2'
                                    style={{
                                        background: 'rgb(254, 254, 254)',
                                    }}
                                >
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        alt='magicImage'
                                        src={wallpaperPath}
                                    />
                                </div>
                            </div>
                        </button>
                        <button
                            className='background-item magic-image-item '
                            style={{ fontSize: 1 }}
                        >
                            <div className='display false'>
                                <div
                                    className='magic-image-display style-2'
                                    style={{
                                        background: 'rgb(135, 101, 89)',
                                    }}
                                >
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        alt='magicImage'
                                        src={wallpaperPath}
                                    />
                                </div>
                            </div>
                        </button>
                    </PublicMagicExpandButton>
                </div>
            </div>
        )
    }

    /**
     * @description 移动端视图渲染
     * @returns {React.ReactElement} 移动端视图
     */
    const mobileInViewRender = (): React.ReactElement => {
        return (
            <div
                id='panel-undefined-control-mobile'
                className='panel-control-mobile magic-backs pack-control-mobile'
                style={{ opacity: 1, transform: 'none' }}
            >
                <PublicMagicMediaControl />
                <div className='panel-control undefined '>
                    <div className='controls'>
                        <PublicMagicPalletCustomize />
                        <div
                            className='panel-control-segment-wrapper'
                            style={{ flexDirection: 'column' }}
                        >
                            <section className='segment-section'>
                                <div className='panel-control-stack'>
                                    <div className='stack-content'>
                                        {currentSelectTab === MagicBackgroundType.SOLID && (
                                            <>
                                                {solidColors.map((color, index) => (
                                                    <button
                                                        key={index}
                                                        className='background-item solid-item'
                                                        onClick={() =>
                                                            handleSolidColorClick(color.background)
                                                        }
                                                    >
                                                        <div className='display false'>
                                                            <div
                                                                style={{
                                                                    background: color.background,
                                                                }}
                                                            />
                                                        </div>
                                                    </button>
                                                ))}
                                            </>
                                        )}

                                        {currentSelectTab === MagicBackgroundType.GRADIENT && (
                                            <>
                                                {gradientColors.map((color, index) => (
                                                    <button
                                                        key={index}
                                                        className='background-item gradient-item'
                                                        onClick={() =>
                                                            handleGradientColorClick(
                                                                color.background,
                                                            )
                                                        }
                                                    >
                                                        <div className='display false'>
                                                            <div
                                                                style={{
                                                                    background: color.background,
                                                                }}
                                                            />
                                                        </div>
                                                    </button>
                                                ))}
                                            </>
                                        )}
                                        {currentSelectTab === MagicBackgroundType.MESH && (
                                            <>
                                                {meshColors.map((color, index) => (
                                                    <div
                                                        key={index}
                                                        className='background-item mesh-item'
                                                        style={{ fontSize: 1 }}
                                                        onClick={() =>
                                                            handleMeshColorClick(
                                                                color.backgroundColor,
                                                                color.backgroundImage,
                                                            )
                                                        }
                                                    >
                                                        <div className='display '>
                                                            <div className='mesh-display mesh-5-1'>
                                                                <div
                                                                    style={{
                                                                        backgroundColor:
                                                                            color.backgroundColor,
                                                                        backgroundImage:
                                                                            color.backgroundImage,
                                                                    }}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </>
                                        )}
                                    </div>
                                </div>
                            </section>
                            <div className='segment-buttons'>
                                {tabsLIs.map((type: MagicBackgroundType) => (
                                    <button
                                        key={type}
                                        onClick={() => setCurrentSelectTab(type)}
                                        type='button'
                                        className={`button default-button small-button undefined-button undefined-blur ${
                                            currentSelectTab === type
                                                ? 'true-active true-round'
                                                : 'false-active false-round'
                                        } undefined`}
                                        style={{ flexDirection: 'row', minWidth: 88 }}
                                    >
                                        <span style={{ textTransform: 'capitalize' }}>{type}</span>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    /**
     * @description PC端视图渲染
     * @returns {React.ReactElement} PC端视图
     */
    const pcInViewRender = (): React.ReactElement => {
        return (
            <>
                <div className='panel-control magic-backs '>
                    <div className='controls'>
                        <span className='h6 title'>
                            Magic
                            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'>
                                <defs>
                                    <mask id='magic_svg__a'>
                                        <path fill='#FFF' d='M0 0h16v16H0Z' />
                                    </mask>
                                </defs>
                                <g mask='url(#magic_svg__a)'>
                                    <path
                                        fill='#F94A73'
                                        d='m11.66 8.02 4.33-1.45-4.34-1.45-1.23-5.13-1.23 5.12-4.34 1.44 4.33 1.44 1.22 5.12 1.22-5.13Z'
                                    />
                                    <path
                                        fill='#FB7A53'
                                        d='m4.66 8.1-.74 3.07-2.6.86 2.6.86.73 3.07.73-3.08 2.6-.87-2.61-.87-.74-3.08Z'
                                    />
                                    <path
                                        fill='#C893E1'
                                        d='M2.88.43 2.24 3.1l-2.26.75 2.25.75.63 2.67.63-2.68 2.25-.76-2.26-.76L2.84.39Z'
                                    />
                                </g>
                            </svg>
                        </span>
                        <PublicMagicMediaControl />
                        <div className='panel-control undefined '>
                            <div className='controls'>
                                <PublicMagicPalletCustomize />
                                {renderSolidColors()}
                                {renderGradientColors()}
                                {renderMeshColors()}
                                {renderImageBackgrounds()}
                            </div>
                        </div>
                    </div>
                </div>
            </>
        )
    }

    /**
     * @description 根据设备类型选择对应的视图渲染
     * @returns {React.ReactElement} 渲染的视图
     */
    if (isMobile) {
        return mobileInViewRender()
    } else {
        return pcInViewRender()
    }
}
