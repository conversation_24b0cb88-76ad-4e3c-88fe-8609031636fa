/**
 * @fileoverview 图片管理系统核心状态管理
 * @description 基于Zustand实现的图片上传、存储、绑定管理系统
 *
 * 核心特性：
 * - 一对多图片绑定：一张图片可以被多个设备同时使用
 * - 设备唯一性约束：每个设备只能绑定一张图片
 * - 智能绑定算法：自动处理绑定冲突和替换逻辑
 * - 向后兼容：保持原有API和交互逻辑不变
 *
 * 数据流设计：
 * 用户操作 → 状态更新 → 设备唯一性检查 → 多组件同步更新
 *
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025.01
 */

import { create } from 'zustand'
import {
    SUPPORTED_IMAGE_TYPES,
    FILE_SIZE_LIMITS,
    validateImageFile,
    formatFileSize,
} from './imageConfig'

/**
 * @enum ImageStatus
 * @description 图片状态枚举 - 追踪图片在系统中的生命周期
 *
 * 状态流转：
 * IDLE → UPLOADING → COMPLETED/ERROR
 */
export enum ImageStatus {
    IDLE = 'idle', // 空闲状态：图片已创建但未开始处理
    UPLOADING = 'uploading', // 上传中：图片正在上传过程中
    COMPLETED = 'completed', // 上传完成：图片已成功处理并可用
    ERROR = 'error', // 上传失败：图片处理过程中发生错误
}

/**
 * @interface ImageItem
 * @description 图片项数据结构 - 系统中每张图片的完整信息
 *
 * 设计原则：
 * - 包含图片的所有必要信息
 * - 支持多设备绑定（deviceIndexes数组）
 * - 保持数据结构的向前兼容性
 */
export interface ImageItem {
    /** 唯一标识符 - 用于图片的全局识别和操作 */
    id: string

    /** 原始文件对象 - 保存用户上传的原始文件 */
    file: File

    /** 预览URL - 通过createObjectURL生成的本地预览地址 */
    preview: string

    /** 当前状态 - 图片在系统中的处理状态 */
    status: ImageStatus

    /**
     * 关联的设备索引数组 - 支持一对多绑定的核心字段
     * - undefined: 图片未绑定到任何设备
     * - [0]: 图片绑定到设备0
     * - [0, 1, 2]: 图片同时绑定到设备0、1、2
     */
    deviceIndexes?: number[]

    /** 图片宽高比 - 用于UI布局和显示优化 */
    aspectRatio: number

    /** 错误信息 - 当status为ERROR时的详细错误描述 */
    error?: string
}

/**
 * @interface DragState
 * @description 拖拽状态管理 - 处理跨组件的拖拽交互
 *
 * 支持的拖拽目标：
 * - 'device-0', 'device-1', 'device-2': 特定设备
 * - 'display': 主画布区域
 * - null: 无拖拽目标
 */
export interface DragState {
    /** 是否正在进行拖拽操作 */
    isDragging: boolean

    /**
     * 拖拽悬停的目标区域
     * - 用于精确控制视觉反馈
     * - 支持设备特定的拖拽响应
     */
    dragOverTarget: string | null

    /** 拖拽位置坐标 - 预留字段，用于未来的高级拖拽功能 */
    dropPosition: { x: number; y: number } | null
}

/**
 * @interface ImageStore
 * @description 图片状态管理接口 - 定义所有图片相关的状态和操作
 *
 * 架构设计：
 * - 状态集中管理：所有图片相关状态统一存储
 * - 操作原子化：每个操作都是原子性的，保证数据一致性
 * - 响应式更新：状态变化自动触发组件重渲染
 */
interface ImageStore {
    // ==================== 状态字段 ====================

    /** 图片列表 - 系统中所有图片的集合 */
    images: ImageItem[]

    /** 拖拽状态 - 当前拖拽操作的状态信息 */
    dragState: DragState

    /** 当前选中的图片ID - 用于图片选择和操作 */
    selectedImageId: string | null

    /** 当前选中的设备索引 - 用于设备特定操作 */
    currentSelectedDevice: number | null

    // ==================== 操作方法 ====================

    /**
     * 添加图片到系统
     * @param files 要添加的文件列表
     * @returns Promise 异步操作完成的Promise
     */
    addImages: (files: File[]) => Promise<void>

    /**
     * 从系统中删除图片
     * @param id 要删除的图片ID
     */
    removeImage: (id: string) => void

    /**
     * 更新图片状态
     * @param id 图片ID
     * @param status 新的状态
     */
    updateImageStatus: (id: string, status: ImageStatus) => void

    /**
     * 智能绑定图片到设备（核心方法）
     * @param id 图片ID
     * @param deviceIndex 目标设备索引
     */
    setImageDevice: (id: string, deviceIndex: number) => void

    /**
     * 从设备解绑图片
     * @param id 图片ID
     * @param deviceIndex 设备索引
     */
    removeImageFromDevice: (id: string, deviceIndex: number) => void

    /**
     * 设置拖拽状态
     * @param state 拖拽状态的部分更新
     */
    setDragState: (state: Partial<DragState>) => void

    /**
     * 设置当前选中的设备
     * @param deviceIndex 设备索引，null表示取消选中
     */
    setCurrentSelectedDevice: (deviceIndex: number | null) => void

    /**
     * 将图片绑定到当前选中的设备
     * @param imageId 图片ID
     */
    selectImageForCurrentDevice: (imageId: string) => void

    /**
     * 获取指定设备的图片列表
     * @param deviceIndex 设备索引，不传则返回未绑定的图片
     * @returns 图片列表
     */
    getImagesByDevice: (deviceIndex?: number) => ImageItem[]

    /**
     * 获取下一个可用的设备索引
     * @returns 可用的设备索引
     */
    getNextAvailableDevice: () => number

    /**
     * 检查图片是否绑定到指定设备
     * @param imageId 图片ID
     * @param deviceIndex 设备索引
     * @returns 是否绑定
     */
    isImageBindToDevice: (imageId: string, deviceIndex: number) => boolean
}

/**
 * @function generateId
 * @description 生成唯一标识符
 *
 * 算法说明：
 * - 使用时间戳确保时间唯一性
 * - 使用随机字符串增强唯一性
 * - 格式：img_时间戳_随机字符串
 *
 * @returns {string} 格式化的唯一ID
 * @example "img_1704067200000_a1b2c3d4e"
 */
export const generateId = (): string => {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substr(2, 9)
    return `img_${timestamp}_${randomStr}`
}

/**
 * @function validateFile
 * @description 验证上传文件是否符合系统要求，包含 sonner 错误提示
 *
 * 验证规则：
 * 1. 文件类型：仅支持 JPG 和 PNG 格式
 * 2. 文件大小：限制在10MB以内，避免内存问题
 * 3. 验证失败时显示 sonner 错误提示
 * 4. 记录详细的错误日志
 *
 * @param {File} file 待验证的文件对象
 * @returns {boolean} 验证结果，true表示文件有效
 */
const validateFile = (file: File): boolean => {
    // 使用统一的验证函数，启用 sonner 错误提示
    const validationResult = validateImageFile(file, {
        allowedTypes: SUPPORTED_IMAGE_TYPES,
        maxFileSize: FILE_SIZE_LIMITS.DEFAULT_MAX_SIZE,
        showToast: true, // 启用 sonner 错误提示
    })

    if (!validationResult.isValid) {
        // 错误提示已由 validateImageFile 函数处理
        // 这里只记录额外的上下文信息
        console.warn(`❌ 文件验证失败: ${file.name} - ${validationResult.errorMessage}`)
        return false
    }

    return true
}

/**
 * @function getImageDimensions
 * @description 异步获取图片的真实尺寸
 *
 * 实现原理：
 * 1. 创建临时Image对象
 * 2. 设置图片源为文件的ObjectURL
 * 3. 监听load事件获取尺寸
 * 4. 自动清理临时URL
 *
 * @param {File} file 图片文件对象
 * @returns {Promise<{width: number, height: number}>} 图片尺寸信息
 */
const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
        const img = new Image()
        const url = URL.createObjectURL(file)

        img.onload = () => {
            // 获取尺寸后立即清理URL，避免内存泄漏
            URL.revokeObjectURL(url)
            resolve({
                width: img.width,
                height: img.height,
            })
        }

        img.onerror = () => {
            // 错误处理：清理URL并抛出错误
            URL.revokeObjectURL(url)
            reject(new Error(`无法获取图片尺寸: ${file.name}`))
        }

        img.src = url
    })
}

/**
 * @function processFiles
 * @description 批量处理文件列表，转换为ImageItem对象
 *
 * 处理流程：
 * 1. 遍历所有文件
 * 2. 验证每个文件的有效性
 * 3. 异步获取图片尺寸信息
 * 4. 创建标准化的ImageItem对象
 * 5. 返回处理成功的图片列表
 *
 * 错误处理：
 * - 无效文件会被跳过，不影响其他文件处理
 * - 处理失败的文件会记录错误日志
 *
 * @param {File[]} files 要处理的文件列表
 * @returns {Promise<ImageItem[]>} 处理后的图片项列表
 */
export const processFiles = async (files: File[]): Promise<ImageItem[]> => {
    const processedItems: ImageItem[] = []

    // 并发处理所有文件以提高性能
    for (const file of files) {
        // 跳过无效文件
        if (!validateFile(file)) {
            continue
        }

        try {
            // 为图片创建预览URL
            const preview = URL.createObjectURL(file)

            // 异步获取图片的真实尺寸
            const { width, height } = await getImageDimensions(file)

            // 创建标准化的ImageItem对象
            const imageItem: ImageItem = {
                id: generateId(), // 生成唯一标识
                file, // 保存原始文件引用
                preview, // 本地预览URL
                status: ImageStatus.COMPLETED, // 本地模式直接标记为完成
                aspectRatio: width / height, // 计算宽高比，用于UI布局
                deviceIndexes: [], // 初始化为空数组，表示未绑定任何设备
            }

            processedItems.push(imageItem)
            console.log(`✅ 文件处理成功: ${file.name} (${width}x${height})`)
        } catch (error) {
            console.error(`❌ 文件处理失败: ${file.name}`, error)
            // 继续处理其他文件，不因单个文件失败而中断
        }
    }

    return processedItems
}

/**
 * @constant useImageStore
 * @description 图片管理Zustand Store - 系统的核心状态管理器
 *
 * 设计特点：
 * - 单一数据源：所有图片相关状态集中管理
 * - 响应式更新：状态变化自动同步到所有消费组件
 * - 操作原子化：每个操作都确保数据一致性
 * - 智能约束：自动维护业务规则和数据约束
 */
export const useImageStore = create<ImageStore>((set, get) => ({
    // ==================== 初始状态 ====================

    /** 图片列表 - 初始为空数组 */
    images: [],

    /** 拖拽状态 - 初始为非拖拽状态 */
    dragState: {
        isDragging: false,
        dragOverTarget: null,
        dropPosition: null,
    },

    /** 当前选中图片 - 初始为未选中 */
    selectedImageId: null,

    /** 当前选中设备 - 初始为未选中 */
    currentSelectedDevice: null,

    // ==================== 操作方法实现 ====================

    /**
     * @method addImages
     * @description 添加图片到系统存储
     *
     * 实现逻辑：
     * 1. 异步处理文件列表，转换为ImageItem
     * 2. 将处理后的图片添加到存储
     * 3. 如果有当前选中设备，自动绑定第一张图片
     * 4. 记录操作日志用于调试
     *
     * 智能绑定：
     * - 优先绑定到用户当前选中的设备
     * - 使用setImageDevice方法确保设备唯一性约束
     *
     * @param {File[]} files 要添加的文件列表
     */
    addImages: async (files: File[]) => {
        console.log(`🚀 开始处理 ${files.length} 个文件`)

        // 异步处理所有文件
        const processedItems = await processFiles(files)

        if (processedItems.length > 0) {
            const { currentSelectedDevice, setImageDevice } = get()

            // 先将图片添加到存储中
            set(state => ({
                images: [...state.images, ...processedItems],
            }))

            // 智能绑定：如果有选中设备，自动绑定第一张图片
            if (currentSelectedDevice !== null && processedItems.length > 0) {
                // 使用setImageDevice确保正确的绑定逻辑和设备唯一性
                setImageDevice(processedItems[0].id, currentSelectedDevice)
                console.log(
                    `🔗 自动绑定图片 ${processedItems[0].id} 到设备 ${currentSelectedDevice}`,
                )
            }

            console.log(`✅ 成功添加 ${processedItems.length} 张图片到系统`)
        } else {
            console.warn('⚠️ 没有有效的图片文件被处理')
        }
    },

    /**
     * @method removeImage
     * @description 从系统中删除指定图片
     *
     * 清理操作：
     * 1. 查找要删除的图片
     * 2. 清理预览URL，防止内存泄漏
     * 3. 从图片列表中移除
     * 4. 清理相关的选中状态
     *
     * 内存管理：
     * - 自动释放createObjectURL创建的URL
     * - 清理可能的图片选中状态
     *
     * @param {string} id 要删除的图片ID
     */
    removeImage: (id: string) => {
        const { images } = get()
        const imageToRemove = images.find(img => img.id === id)

        if (imageToRemove) {
            // 重要：清理预览URL以防止内存泄漏
            URL.revokeObjectURL(imageToRemove.preview)

            // 更新状态：移除图片并清理相关选中状态
            set(state => ({
                images: state.images.filter(img => img.id !== id),
                selectedImageId: state.selectedImageId === id ? null : state.selectedImageId,
            }))

            console.log(`🗑️ 图片已删除: ${id}`)
        } else {
            console.warn(`⚠️ 要删除的图片不存在: ${id}`)
        }
    },

    /**
     * @method updateImageStatus
     * @description 更新指定图片的状态
     *
     * 用途：
     * - 追踪图片处理进度
     * - 处理上传过程中的状态变化
     * - 标记处理错误的图片
     *
     * @param {string} id 图片ID
     * @param {ImageStatus} status 新的状态值
     */
    updateImageStatus: (id: string, status: ImageStatus) => {
        set(state => ({
            images: state.images.map(img => (img.id === id ? { ...img, status } : img)),
        }))
        console.log(`📊 图片状态更新: ${id} → ${status}`)
    },

    /**
     * @method setImageDevice
     * @description 🔥 核心方法：智能绑定图片到设备
     *
     * 算法核心 - 一对多绑定 + 设备唯一性约束：
     *
     * 1. 图片维度（一对多）：
     *    - 将目标图片添加到指定设备的绑定列表
     *    - 一张图片可以同时绑定到多个设备
     *
     * 2. 设备维度（一对一）：
     *    - 从其他所有图片中移除对该设备的绑定
     *    - 确保每个设备只能绑定一张图片
     *
     * 3. 结果：
     *    - 图片A可以同时显示在设备1、2、3上
     *    - 设备1只能显示图片A，不能同时显示图片B
     *
     * 实现细节：
     * - 原子操作：整个绑定过程在单次状态更新中完成
     * - 冲突解决：自动解决绑定冲突，无需手动干预
     * - 性能优化：只遍历一次图片列表完成所有操作
     *
     * @param {string} id 要绑定的图片ID
     * @param {number} deviceIndex 目标设备索引
     */
    setImageDevice: (id: string, deviceIndex: number) => {
        set(state => ({
            images: state.images.map(img => {
                if (img.id === id) {
                    // 步骤1：将目标图片绑定到指定设备
                    const currentDevices = img.deviceIndexes || []
                    if (!currentDevices.includes(deviceIndex)) {
                        return {
                            ...img,
                            deviceIndexes: [...currentDevices, deviceIndex],
                        }
                    }
                    return img
                } else {
                    // 步骤2：从其他图片中移除对该设备的绑定（确保设备唯一性）
                    const currentDevices = img.deviceIndexes || []
                    if (currentDevices.includes(deviceIndex)) {
                        return {
                            ...img,
                            deviceIndexes: currentDevices.filter(idx => idx !== deviceIndex),
                        }
                    }
                    return img
                }
            }),
        }))
        console.log(`🔗 智能绑定完成: 图片 ${id} → 设备 ${deviceIndex}，原有绑定已自动清理`)
    },

    /**
     * @method removeImageFromDevice
     * @description 从指定设备解绑图片
     *
     * 用途：
     * - 精确控制图片的设备绑定
     * - 支持部分解绑（图片仍可绑定到其他设备）
     * - 用于高级的绑定管理功能
     *
     * @param {string} id 图片ID
     * @param {number} deviceIndex 要解绑的设备索引
     */
    removeImageFromDevice: (id: string, deviceIndex: number) => {
        set(state => ({
            images: state.images.map(img => {
                if (img.id === id) {
                    const currentDevices = img.deviceIndexes || []
                    return {
                        ...img,
                        deviceIndexes: currentDevices.filter(idx => idx !== deviceIndex),
                    }
                }
                return img
            }),
        }))
        console.log(`🔓 解绑完成: 图片 ${id} 从设备 ${deviceIndex} 解绑`)
    },

    /**
     * @method setDragState
     * @description 更新拖拽状态
     *
     * 支持部分状态更新：
     * - 只更新传入的字段，保持其他字段不变
     * - 用于精确控制拖拽交互过程
     *
     * @param {Partial<DragState>} state 要更新的拖拽状态字段
     */
    setDragState: (state: Partial<DragState>) => {
        set(currentState => ({
            dragState: {
                ...currentState.dragState,
                ...state,
            },
        }))
    },

    /**
     * @method setCurrentSelectedDevice
     * @description 设置当前选中的设备
     *
     * 用途：
     * - 记录用户当前操作的设备上下文
     * - 用于智能绑定和设备特定操作
     * - 支持设备切换和选择取消
     *
     * @param {number | null} deviceIndex 设备索引，null表示取消选中
     */
    setCurrentSelectedDevice: (deviceIndex: number | null) => {
        set(state => ({
            currentSelectedDevice: deviceIndex,
        }))
        console.log(`📱 当前选中设备: ${deviceIndex !== null ? `设备 ${deviceIndex}` : '无'}`)
    },

    /**
     * @method selectImageForCurrentDevice
     * @description 将指定图片绑定到当前选中的设备
     *
     * 业务逻辑：
     * 1. 检查是否有选中的设备
     * 2. 验证图片是否存在
     * 3. 调用setImageDevice执行智能绑定
     *
     * 错误处理：
     * - 无选中设备时给出警告
     * - 图片不存在时给出错误提示
     *
     * @param {string} imageId 要绑定的图片ID
     */
    selectImageForCurrentDevice: (imageId: string) => {
        const { currentSelectedDevice, images, setImageDevice } = get()

        // 检查前置条件：必须有选中的设备
        if (currentSelectedDevice === null) {
            console.warn('⚠️ 没有选中的设备，无法进行图片绑定操作')
            return
        }

        // 验证图片存在性
        const targetImage = images.find(img => img.id === imageId)
        if (!targetImage) {
            console.warn(`⚠️ 图片不存在: ${imageId}`)
            return
        }

        // 执行智能绑定（setImageDevice已包含所有绑定逻辑）
        setImageDevice(imageId, currentSelectedDevice)
        console.log(`🎯 图片选择绑定: ${imageId} → 设备 ${currentSelectedDevice}`)
    },

    /**
     * @method getImagesByDevice
     * @description 查询指定设备的图片列表
     *
     * 查询模式：
     * - 传入deviceIndex：返回绑定到该设备的所有图片
     * - 不传参数：返回未绑定任何设备的图片
     *
     * 用途：
     * - 设备特定的图片展示
     * - 未绑定图片的管理
     * - 数据统计和分析
     *
     * @param {number} [deviceIndex] 设备索引，可选
     * @returns {ImageItem[]} 匹配条件的图片列表
     */
    getImagesByDevice: (deviceIndex?: number) => {
        const { images } = get()

        if (deviceIndex === undefined) {
            // 返回未绑定任何设备的图片
            return images.filter(img => !img.deviceIndexes || img.deviceIndexes.length === 0)
        }

        // 返回绑定到指定设备的图片
        return images.filter(img => img.deviceIndexes && img.deviceIndexes.includes(deviceIndex))
    },

    /**
     * @method getNextAvailableDevice
     * @description 获取下一个可用的设备索引
     *
     * 算法：
     * 1. 收集所有已使用的设备索引
     * 2. 从0开始查找第一个未使用的索引
     * 3. 返回可用的设备索引
     *
     * 用途：
     * - 自动分配图片到设备
     * - 智能设备选择
     * - 避免设备冲突
     *
     * @returns {number} 下一个可用的设备索引
     */
    getNextAvailableDevice: () => {
        const { images } = get()
        const usedDevices = new Set<number>()

        // 收集所有已被使用的设备索引
        images.forEach(img => {
            if (img.deviceIndexes) {
                img.deviceIndexes.forEach(idx => usedDevices.add(idx))
            }
        })

        // 从0开始找第一个未使用的设备索引
        let deviceIndex = 0
        while (usedDevices.has(deviceIndex)) {
            deviceIndex++
        }

        return deviceIndex
    },

    /**
     * @method isImageBindToDevice
     * @description 检查图片是否绑定到指定设备
     *
     * 用途：
     * - UI状态判断（高亮、选中状态）
     * - 业务逻辑判断
     * - 绑定状态验证
     *
     * @param {string} imageId 图片ID
     * @param {number} deviceIndex 设备索引
     * @returns {boolean} 绑定状态，true表示已绑定
     */
    isImageBindToDevice: (imageId: string, deviceIndex: number) => {
        const { images } = get()
        const image = images.find(img => img.id === imageId)
        return image?.deviceIndexes?.includes(deviceIndex) || false
    },
}))

// ==================== 导出的工具函数 ====================

/**
 * @function getImageForDevice
 * @description 获取指定设备的第一张图片（向后兼容方法）
 *
 * 兼容性说明：
 * - 保持与原有API的兼容性
 * - 从多张绑定图片中返回第一张
 * - 用于现有组件的无缝升级
 *
 * @param {number} deviceIndex 设备索引
 * @returns {ImageItem | null} 设备的第一张图片，无图片时返回null
 */
export const getImageForDevice = (deviceIndex: number): ImageItem | null => {
    const images = useImageStore.getState().images
    return images.find(img => img.deviceIndexes && img.deviceIndexes.includes(deviceIndex)) || null
}

/**
 * @function getAllImagesForDevice
 * @description 获取指定设备的所有图片（新增功能方法）
 *
 * 扩展功能：
 * - 支持获取设备的完整图片列表
 * - 用于高级图片管理功能
 * - 为未来功能扩展预留接口
 *
 * @param {number} deviceIndex 设备索引
 * @returns {ImageItem[]} 设备绑定的所有图片
 */
export const getAllImagesForDevice = (deviceIndex: number): ImageItem[] => {
    const images = useImageStore.getState().images
    return images.filter(img => img.deviceIndexes && img.deviceIndexes.includes(deviceIndex))
}

/**
 * @function getAllImages
 * @description 获取系统中的所有图片
 *
 * 用途：
 * - 全局图片管理
 * - 数据导出和备份
 * - 系统状态查询
 *
 * @returns {ImageItem[]} 系统中的所有图片
 */
export const getAllImages = (): ImageItem[] => {
    return useImageStore.getState().images
}

/**
 * @function getCurrentSelectedDevice
 * @description 获取当前选中的设备索引
 *
 * 用途：
 * - 获取用户当前操作上下文
 * - 组件间状态共享
 * - 设备特定逻辑判断
 *
 * @returns {number | null} 当前选中的设备索引
 */
export const getCurrentSelectedDevice = (): number | null => {
    return useImageStore.getState().currentSelectedDevice
}
